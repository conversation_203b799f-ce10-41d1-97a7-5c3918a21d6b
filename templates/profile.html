{% extends "base.html" %}

{% block title %}Profile - Event Planner{% endblock %}

{% block content %}
<!-- Profile Header -->
<div class="card">
    <div class="flex justify-between align-center">
        <div>
            <h1>👤 My Profile</h1>
            <p>Welcome back, <span data-user-email id="profile-email">Loading...</span></p>
        </div>
        <div>
            <button id="refresh-profile" class="btn btn-secondary">🔄 Refresh</button>
        </div>
    </div>
</div>

<!-- User Stats Dashboard -->
<div class="card">
    <h2>📊 Your Activity Stats</h2>
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number" id="stat-total-events">0</div>
            <div class="stat-label">Total Events Saved</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="stat-events-attended">0</div>
            <div class="stat-label">Events Attended</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="stat-alerts-sent">0</div>
            <div class="stat-label">Alerts Received</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="stat-this-month">0</div>
            <div class="stat-label">This Month</div>
        </div>
    </div>
</div>

<!-- Two Column Layout -->
<div class="grid grid-2">
    <!-- Left Column: Preferences & Settings -->
    <div>
        <!-- Activity Preferences -->
        <div class="card">
            <h3>🎯 Activity Preferences</h3>
            <p>Select your favorite activities to get personalized event recommendations:</p>
            
            <form id="preferences-form">
                <div class="preferences-grid">
                    <div class="preference-item">
                        <input type="checkbox" id="hiking" name="activities" value="hiking">
                        <label for="hiking">🥾 Hiking</label>
                    </div>
                    <div class="preference-item">
                        <input type="checkbox" id="concerts" name="activities" value="concerts">
                        <label for="concerts">🎵 Outdoor Concerts</label>
                    </div>
                    <div class="preference-item">
                        <input type="checkbox" id="fishing" name="activities" value="fishing">
                        <label for="fishing">🎣 Fishing</label>
                    </div>
                    <div class="preference-item">
                        <input type="checkbox" id="camping" name="activities" value="camping">
                        <label for="camping">🏕️ Camping</label>
                    </div>
                    <div class="preference-item">
                        <input type="checkbox" id="sports" name="activities" value="sports">
                        <label for="sports">⚽ Sports Events</label>
                    </div>
                    <div class="preference-item">
                        <input type="checkbox" id="festivals" name="activities" value="festivals">
                        <label for="festivals">🎪 Festivals</label>
                    </div>
                    <div class="preference-item">
                        <input type="checkbox" id="picnics" name="activities" value="picnics">
                        <label for="picnics">🧺 Picnics</label>
                    </div>
                    <div class="preference-item">
                        <input type="checkbox" id="photography" name="activities" value="photography">
                        <label for="photography">📸 Photography</label>
                    </div>
                </div>
                
                <div class="mt-2">
                    <button type="submit" class="btn">💾 Save Preferences</button>
                    <span id="preferences-status" class="ml-1"></span>
                </div>
            </form>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <h3>⚡ Quick Actions</h3>
            <div class="grid">
                <a href="/events" class="btn">📅 Plan New Event</a>
                <button id="generate-sample-alert" class="btn btn-secondary">🔔 Test Alert System</button>
                <button id="check-weather-alerts" class="btn btn-secondary">🌦️ Check Weather Alerts</button>
            </div>
        </div>
    </div>

    <!-- Right Column: Events & Alerts -->
    <div>
        <!-- Saved Events -->
        <div class="card">
            <div class="flex justify-between align-center mb-1">
                <h3>📅 My Saved Events</h3>
                <button id="filter-events" class="btn btn-secondary">🔍 Filter by Preferences</button>
            </div>
            
            <div id="saved-events-container">
                <div class="loading">
                    <div class="spinner"></div>
                    Loading your events...
                </div>
            </div>
            
            <div class="mt-1">
                <a href="/events" class="btn btn-secondary">➕ Add New Event</a>
            </div>
        </div>

        <!-- Recent Alerts -->
        <div class="card">
            <h3>🔔 Recent Alerts</h3>
            <div id="alerts-container">
                <div class="loading">
                    <div class="spinner"></div>
                    Loading alerts...
                </div>
            </div>
            
            <div class="mt-1">
                <button id="mark-all-read" class="btn btn-secondary">✅ Mark All Read</button>
            </div>
        </div>
    </div>
</div>

<!-- Sample Event Modal (for demo purposes) -->
<div id="sample-event-modal" class="modal" style="display: none;">
    <div class="modal-content card">
        <h3>🎉 Sample Event</h3>
        <p>This is a demo event to show how the system works!</p>
        
        <div class="form-group">
            <label>Event Title:</label>
            <input type="text" id="sample-event-title" value="Outdoor Concert in Central Park">
        </div>
        
        <div class="form-group">
            <label>Event Date:</label>
            <input type="date" id="sample-event-date" value="2025-10-15">
        </div>
        
        <div class="form-group">
            <label>Event Type:</label>
            <select id="sample-event-type">
                <option value="concerts">Outdoor Concert</option>
                <option value="hiking">Hiking</option>
                <option value="festivals">Festival</option>
                <option value="sports">Sports Event</option>
            </select>
        </div>
        
        <div class="mt-2">
            <button id="save-sample-event" class="btn">💾 Save Event</button>
            <button id="close-modal" class="btn btn-secondary">❌ Cancel</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/profile.js') }}"></script>

<style>
/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

/* Enhanced preference grid */
.preferences-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin: 1rem 0;
}

.preference-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.preference-item:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.preference-item input[type="checkbox"]:checked + label {
    color: #667eea;
    font-weight: bold;
}

.preference-item input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Event card enhancements */
.event-card {
    transition: transform 0.2s ease;
}

.event-card:hover {
    transform: translateY(-2px);
}

/* Loading states */
.loading {
    text-align: center;
    padding: 2rem;
    color: #666;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-saved .status-indicator {
    background: #4caf50;
}

.status-past .status-indicator {
    background: #9e9e9e;
}
</style>
{% endblock %}
