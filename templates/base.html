<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Event Planner{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- Header Navigation -->
    <header class="header">
        <nav class="nav">
            <div class="logo">🌤️ EventPlanner</div>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li data-auth style="display: none;"><a href="/profile">Profile</a></li>
                <li data-auth style="display: none;"><a href="/events">Events</a></li>
                <li data-auth style="display: none;"><a href="#" id="signout-btn">Sign Out</a></li>
            </ul>
        </nav>
    </header>

    <!-- Message Container for Alerts -->
    <div id="message-container" class="container"></div>

    <!-- Main Content -->
    <main class="container">
        {% block content %}{% endblock %}
    </main>

    <!-- Supabase Client -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- Initialize Supabase -->
    <script>
        // Initialize Supabase client
        const supabaseUrl = '{{ config.SUPABASE_URL or "YOUR_SUPABASE_URL" }}';
        const supabaseKey = '{{ config.SUPABASE_KEY or "YOUR_SUPABASE_KEY" }}';
        
        let supabaseClient;
        
        if (supabaseUrl !== 'YOUR_SUPABASE_URL' && supabaseKey !== 'YOUR_SUPABASE_KEY') {
            supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);
        } else {
            console.warn('Supabase not configured. Please set up your .env file.');
            // Create a mock client for development
            supabaseClient = {
                auth: {
                    getUser: () => Promise.resolve({ data: { user: null } }),
                    signUp: () => Promise.resolve({ data: null, error: { message: 'Supabase not configured' } }),
                    signInWithPassword: () => Promise.resolve({ data: null, error: { message: 'Supabase not configured' } }),
                    signOut: () => Promise.resolve({ error: null })
                }
            };
        }
    </script>

    <!-- Auth Manager -->
    <script src="{{ url_for('static', filename='js/auth.js') }}"></script>
    
    <!-- Initialize Auth -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            authManager.init(supabaseClient);
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
