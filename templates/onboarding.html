{% extends "base.html" %}

{% block title %}Welcome - Event Planner{% endblock %}

{% block content %}
<!-- Onboarding Container -->
<div class="onboarding-container">
    <!-- Step 1: Welcome -->
    <div class="onboarding-step active" id="step-1">
        <div class="card text-center">
            <div class="onboarding-icon">🎉</div>
            <h1>Welcome to EventPlanner!</h1>
            <p>We're excited to help you plan amazing events with personalized recommendations and weather integration.</p>
            
            <div class="onboarding-features">
                <div class="feature-item">
                    <div class="feature-icon">🎯</div>
                    <h3>Personalized Events</h3>
                    <p>Get recommendations based on your interests</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🌤️</div>
                    <h3>Weather Integration</h3>
                    <p>Real-time weather alerts for your events</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📊</div>
                    <h3>Activity Tracking</h3>
                    <p>Track your event history and insights</p>
                </div>
            </div>
            
            <button class="btn btn-large" onclick="onboardingManager.nextStep()">
                Get Started 🚀
            </button>
        </div>
    </div>

    <!-- Step 2: Set Preferences -->
    <div class="onboarding-step" id="step-2">
        <div class="card">
            <div class="onboarding-icon text-center">🎯</div>
            <h2 class="text-center">What activities do you enjoy?</h2>
            <p class="text-center">Select your favorite activities to get personalized event recommendations:</p>
            
            <form id="onboarding-preferences-form">
                <div class="preferences-grid-large">
                    <div class="preference-card" data-activity="hiking">
                        <div class="preference-icon">🥾</div>
                        <h4>Hiking</h4>
                        <p>Mountain trails and nature walks</p>
                        <input type="checkbox" name="activities" value="hiking" id="onb-hiking">
                    </div>
                    
                    <div class="preference-card" data-activity="concerts">
                        <div class="preference-icon">🎵</div>
                        <h4>Outdoor Concerts</h4>
                        <p>Live music and performances</p>
                        <input type="checkbox" name="activities" value="concerts" id="onb-concerts">
                    </div>
                    
                    <div class="preference-card" data-activity="fishing">
                        <div class="preference-icon">🎣</div>
                        <h4>Fishing</h4>
                        <p>Lakes, rivers, and peaceful waters</p>
                        <input type="checkbox" name="activities" value="fishing" id="onb-fishing">
                    </div>
                    
                    <div class="preference-card" data-activity="camping">
                        <div class="preference-icon">🏕️</div>
                        <h4>Camping</h4>
                        <p>Outdoor adventures and stargazing</p>
                        <input type="checkbox" name="activities" value="camping" id="onb-camping">
                    </div>
                    
                    <div class="preference-card" data-activity="sports">
                        <div class="preference-icon">⚽</div>
                        <h4>Sports Events</h4>
                        <p>Games, matches, and competitions</p>
                        <input type="checkbox" name="activities" value="sports" id="onb-sports">
                    </div>
                    
                    <div class="preference-card" data-activity="festivals">
                        <div class="preference-icon">🎪</div>
                        <h4>Festivals</h4>
                        <p>Cultural events and celebrations</p>
                        <input type="checkbox" name="activities" value="festivals" id="onb-festivals">
                    </div>
                    
                    <div class="preference-card" data-activity="picnics">
                        <div class="preference-icon">🧺</div>
                        <h4>Picnics</h4>
                        <p>Family gatherings and outdoor meals</p>
                        <input type="checkbox" name="activities" value="picnics" id="onb-picnics">
                    </div>
                    
                    <div class="preference-card" data-activity="photography">
                        <div class="preference-icon">📸</div>
                        <h4>Photography</h4>
                        <p>Capturing beautiful moments</p>
                        <input type="checkbox" name="activities" value="photography" id="onb-photography">
                    </div>
                </div>
                
                <div class="onboarding-actions">
                    <button type="button" class="btn btn-secondary" onclick="onboardingManager.prevStep()">
                        ← Back
                    </button>
                    <button type="button" class="btn" onclick="onboardingManager.nextStep()">
                        Continue →
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Step 3: Features Overview -->
    <div class="onboarding-step" id="step-3">
        <div class="card">
            <div class="onboarding-icon text-center">📱</div>
            <h2 class="text-center">Here's how EventPlanner works</h2>
            
            <div class="feature-walkthrough">
                <div class="walkthrough-item">
                    <div class="walkthrough-number">1</div>
                    <div class="walkthrough-content">
                        <h4>Plan Your Events</h4>
                        <p>Use our event planning form to create and save your upcoming activities. We'll help you choose the perfect date and location.</p>
                    </div>
                </div>
                
                <div class="walkthrough-item">
                    <div class="walkthrough-number">2</div>
                    <div class="walkthrough-content">
                        <h4>Get Weather Alerts</h4>
                        <p>Receive real-time weather notifications for your events. We'll warn you about rain, storms, or perfect weather conditions.</p>
                    </div>
                </div>
                
                <div class="walkthrough-item">
                    <div class="walkthrough-number">3</div>
                    <div class="walkthrough-content">
                        <h4>Track Your Activities</h4>
                        <p>View your event history, see statistics about your activities, and get personalized recommendations for future events.</p>
                    </div>
                </div>
            </div>
            
            <div class="onboarding-actions">
                <button type="button" class="btn btn-secondary" onclick="onboardingManager.prevStep()">
                    ← Back
                </button>
                <button type="button" class="btn" onclick="onboardingManager.nextStep()">
                    Continue →
                </button>
            </div>
        </div>
    </div>

    <!-- Step 4: Complete -->
    <div class="onboarding-step" id="step-4">
        <div class="card text-center">
            <div class="onboarding-icon">✅</div>
            <h2>You're all set!</h2>
            <p>Your preferences have been saved and you're ready to start planning amazing events.</p>
            
            <div class="completion-summary">
                <h4>Your Selected Activities:</h4>
                <div id="selected-activities" class="selected-activities-list">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
            
            <div class="onboarding-actions">
                <button type="button" class="btn btn-secondary" onclick="onboardingManager.prevStep()">
                    ← Back
                </button>
                <button type="button" class="btn btn-large" onclick="onboardingManager.completeOnboarding()">
                    Start Planning Events! 🎉
                </button>
            </div>
        </div>
    </div>

    <!-- Progress Indicator -->
    <div class="progress-indicator">
        <div class="progress-step active" data-step="1">1</div>
        <div class="progress-line"></div>
        <div class="progress-step" data-step="2">2</div>
        <div class="progress-line"></div>
        <div class="progress-step" data-step="3">3</div>
        <div class="progress-line"></div>
        <div class="progress-step" data-step="4">4</div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/onboarding.js') }}"></script>

<style>
/* Onboarding Styles */
.onboarding-container {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.onboarding-step {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.onboarding-step.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.onboarding-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.btn-large {
    padding: 15px 30px;
    font-size: 1.2rem;
    margin: 1rem;
}

/* Features Grid */
.onboarding-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.feature-item {
    text-align: center;
    padding: 1rem;
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* Preferences Grid */
.preferences-grid-large {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.preference-card {
    background: white;
    border: 3px solid #e1e5e9;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.preference-card:hover {
    border-color: #667eea;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.preference-card.selected {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.preference-card input[type="checkbox"] {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
}

.preference-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.preference-card h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.preference-card p {
    color: #666;
    font-size: 0.9rem;
}

/* Feature Walkthrough */
.feature-walkthrough {
    margin: 2rem 0;
}

.walkthrough-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    gap: 1rem;
}

.walkthrough-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    flex-shrink: 0;
}

.walkthrough-content h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.walkthrough-content p {
    color: #666;
    line-height: 1.6;
}

/* Progress Indicator */
.progress-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 3rem;
    padding: 2rem 0;
}

.progress-step {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e1e5e9;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    transition: all 0.3s ease;
}

.progress-step.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.progress-step.completed {
    background: #4caf50;
    color: white;
}

.progress-line {
    width: 60px;
    height: 3px;
    background: #e1e5e9;
    margin: 0 10px;
}

.progress-line.completed {
    background: #4caf50;
}

/* Actions */
.onboarding-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    gap: 1rem;
}

/* Completion Summary */
.completion-summary {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
}

.selected-activities-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 1rem;
}

.activity-tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .onboarding-features {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .preferences-grid-large {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .onboarding-actions {
        flex-direction: column;
    }
    
    .progress-indicator {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .progress-line {
        width: 30px;
    }
}
</style>
{% endblock %}
