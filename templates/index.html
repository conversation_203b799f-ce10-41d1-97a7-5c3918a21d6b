{% extends "base.html" %}

{% block title %}Welcome - Event Planner{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="card text-center" data-no-auth>
    <h1>🌤️ Welcome to EventPlanner</h1>
    <p>Plan your perfect events with real-time weather integration and personalized alerts!</p>
    
    <div class="grid grid-2 mt-2">
        <!-- Sign In Form -->
        <div class="card">
            <h2>Sign In</h2>
            <form id="signin-form">
                <div class="form-group">
                    <label for="signin-email">Email:</label>
                    <input type="email" id="signin-email" required>
                </div>
                <div class="form-group">
                    <label for="signin-password">Password:</label>
                    <input type="password" id="signin-password" required>
                </div>
                <button type="submit" class="btn">Sign In</button>
            </form>
        </div>

        <!-- Sign Up Form -->
        <div class="card">
            <h2>Create Account</h2>
            <form id="signup-form">
                <div class="form-group">
                    <label for="signup-email">Email:</label>
                    <input type="email" id="signup-email" required>
                </div>
                <div class="form-group">
                    <label for="signup-password">Password:</label>
                    <input type="password" id="signup-password" required minlength="6">
                </div>
                <button type="submit" class="btn">Create Account</button>
            </form>
        </div>
    </div>
</div>

<!-- Authenticated User Dashboard -->
<div data-auth style="display: none;">
    <div class="card text-center">
        <h1>Welcome back, <span data-user-email></span>! 👋</h1>
        <p>Ready to plan your next adventure?</p>
        
        <div class="flex justify-between mt-2">
            <a href="/profile" class="btn">View Profile</a>
            <a href="/events" class="btn btn-secondary">Plan Event</a>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number" id="quick-total-events">0</div>
            <div class="stat-label">Total Events</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="quick-alerts-sent">0</div>
            <div class="stat-label">Alerts Received</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="quick-events-attended">0</div>
            <div class="stat-label">Events Attended</div>
        </div>
    </div>

    <!-- Recent Alerts -->
    <div class="card">
        <h3>Recent Alerts</h3>
        <div id="recent-alerts-container">
            <div class="loading">
                <div class="spinner"></div>
                Loading recent alerts...
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="card" data-no-auth>
    <h2>Features</h2>
    <div class="grid grid-3">
        <div class="text-center">
            <h3>🎯 Personalized Events</h3>
            <p>Get event recommendations based on your interests and preferences</p>
        </div>
        <div class="text-center">
            <h3>🌦️ Weather Integration</h3>
            <p>Real-time weather alerts for your planned outdoor activities</p>
        </div>
        <div class="text-center">
            <h3>📊 Activity Tracking</h3>
            <p>Track your event history and get insights on your activities</p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load quick stats when user is authenticated
    function loadQuickStats() {
        const user = authManager.getCurrentUser();
        if (!user) return;

        // Mock data for demo - replace with actual API calls
        fetch(`/api/user/profile?user_id=${user.id}`)
            .then(response => response.json())
            .then(data => {
                if (data.stats) {
                    document.getElementById('quick-total-events').textContent = data.stats.total_events || 0;
                    document.getElementById('quick-alerts-sent').textContent = data.stats.alerts_sent || 0;
                    document.getElementById('quick-events-attended').textContent = data.stats.events_attended || 0;
                }

                // Load recent alerts
                const alertsContainer = document.getElementById('recent-alerts-container');
                if (data.recent_alerts && data.recent_alerts.length > 0) {
                    alertsContainer.innerHTML = '';
                    data.recent_alerts.slice(0, 3).forEach(alert => {
                        const alertEl = document.createElement('div');
                        alertEl.className = `alert alert-${alert.type || 'info'}`;
                        alertEl.innerHTML = `
                            <strong>${new Date(alert.created_at).toLocaleDateString()}</strong><br>
                            ${alert.message}
                        `;
                        alertsContainer.appendChild(alertEl);
                    });
                } else {
                    alertsContainer.innerHTML = '<p>No recent alerts</p>';
                }
            })
            .catch(error => {
                console.error('Error loading stats:', error);
                document.getElementById('recent-alerts-container').innerHTML = '<p>Error loading alerts</p>';
            });
    }

    // Listen for auth state changes
    const originalUpdateUI = authManager.updateUI;
    authManager.updateUI = function() {
        originalUpdateUI.call(this);
        if (this.isAuthenticated()) {
            setTimeout(loadQuickStats, 100); // Small delay to ensure UI is updated
        }
    };
});
</script>
{% endblock %}
