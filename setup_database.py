#!/usr/bin/env python3
"""
Database Setup Script for EventPlanner
Creates all necessary tables and policies in Supabase
"""

import os
from dotenv import load_dotenv
from supabase import create_client

# Load environment variables
load_dotenv()

SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

def setup_database():
    """Set up the database tables and policies"""
    
    if not SUPABASE_URL or not SUPABASE_KEY:
        print("❌ Supabase credentials not found in .env file")
        return False
    
    print("🔗 Connecting to Supabase...")
    supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
    
    # SQL commands to create tables
    sql_commands = [
        # Users table (extends auth.users)
        """
        CREATE TABLE IF NOT EXISTS users (
            id UUID REFERENCES auth.users(id) PRIMARY KEY,
            email TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        # User preferences table
        """
        CREATE TABLE IF NOT EXISTS preferences (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            activities TEXT[] DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        # Saved events table
        """
        CREATE TABLE IF NOT EXISTS saved_events (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            event_title TEXT NOT NULL,
            event_date DATE,
            event_location TEXT,
            event_type TEXT,
            weather_status TEXT DEFAULT 'unknown',
            status TEXT DEFAULT 'saved' CHECK (status IN ('saved', 'past', 'cancelled')),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        # User stats table
        """
        CREATE TABLE IF NOT EXISTS stats (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            total_events INTEGER DEFAULT 0,
            events_attended INTEGER DEFAULT 0,
            alerts_sent INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        # Alerts table
        """
        CREATE TABLE IF NOT EXISTS alerts (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            message TEXT NOT NULL,
            type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'success', 'error')),
            read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """
    ]
    
    # RLS policies
    rls_policies = [
        # Enable RLS on all tables
        "ALTER TABLE users ENABLE ROW LEVEL SECURITY;",
        "ALTER TABLE preferences ENABLE ROW LEVEL SECURITY;",
        "ALTER TABLE saved_events ENABLE ROW LEVEL SECURITY;",
        "ALTER TABLE stats ENABLE ROW LEVEL SECURITY;",
        "ALTER TABLE alerts ENABLE ROW LEVEL SECURITY;",
        
        # Users policies
        """
        CREATE POLICY IF NOT EXISTS "Users can view own profile" ON users
            FOR SELECT USING (auth.uid() = id);
        """,
        """
        CREATE POLICY IF NOT EXISTS "Users can update own profile" ON users
            FOR UPDATE USING (auth.uid() = id);
        """,
        """
        CREATE POLICY IF NOT EXISTS "Users can insert own profile" ON users
            FOR INSERT WITH CHECK (auth.uid() = id);
        """,
        
        # Preferences policies
        """
        CREATE POLICY IF NOT EXISTS "Users can manage own preferences" ON preferences
            FOR ALL USING (auth.uid() = user_id);
        """,
        
        # Saved events policies
        """
        CREATE POLICY IF NOT EXISTS "Users can manage own events" ON saved_events
            FOR ALL USING (auth.uid() = user_id);
        """,
        
        # Stats policies
        """
        CREATE POLICY IF NOT EXISTS "Users can view own stats" ON stats
            FOR SELECT USING (auth.uid() = user_id);
        """,
        """
        CREATE POLICY IF NOT EXISTS "Users can update own stats" ON stats
            FOR UPDATE USING (auth.uid() = user_id);
        """,
        """
        CREATE POLICY IF NOT EXISTS "Users can insert own stats" ON stats
            FOR INSERT WITH CHECK (auth.uid() = user_id);
        """,
        
        # Alerts policies
        """
        CREATE POLICY IF NOT EXISTS "Users can manage own alerts" ON alerts
            FOR ALL USING (auth.uid() = user_id);
        """
    ]
    
    # Create indexes
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_preferences_user_id ON preferences(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_saved_events_user_id ON saved_events(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_stats_user_id ON stats(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_alerts_user_id ON alerts(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_alerts_created_at ON alerts(created_at DESC);"
    ]
    
    try:
        print("📋 Creating tables...")
        for i, sql in enumerate(sql_commands, 1):
            print(f"   Creating table {i}/{len(sql_commands)}...")
            result = supabase.rpc('exec_sql', {'sql': sql}).execute()
            
        print("🔒 Setting up Row Level Security policies...")
        for i, policy in enumerate(rls_policies, 1):
            print(f"   Creating policy {i}/{len(rls_policies)}...")
            result = supabase.rpc('exec_sql', {'sql': policy}).execute()
            
        print("📊 Creating indexes...")
        for i, index in enumerate(indexes, 1):
            print(f"   Creating index {i}/{len(indexes)}...")
            result = supabase.rpc('exec_sql', {'sql': index}).execute()
            
        print("✅ Database setup completed successfully!")
        print("🎉 Your EventPlanner database is ready to use!")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up database: {e}")
        print("💡 You may need to run the SQL commands manually in your Supabase dashboard")
        print("📖 See SUPABASE_SETUP.md for manual setup instructions")
        return False

if __name__ == "__main__":
    print("🗄️  EventPlanner Database Setup")
    print("=" * 40)
    
    success = setup_database()
    
    if success:
        print("\n🚀 Next steps:")
        print("   1. Restart your Flask app: python3 start.py")
        print("   2. Visit http://localhost:5000")
        print("   3. Create an account and test the features!")
    else:
        print("\n🔧 Manual setup required:")
        print("   1. Go to your Supabase dashboard")
        print("   2. Open SQL Editor")
        print("   3. Run the commands from SUPABASE_SETUP.md")
