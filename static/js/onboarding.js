// Onboarding flow manager
class OnboardingManager {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 4;
        this.selectedActivities = [];
        this.currentUser = null;
    }

    // Initialize onboarding
    async init() {
        this.currentUser = authManager.getCurrentUser();
        
        if (!this.currentUser) {
            window.location.href = '/';
            return;
        }

        // Check if user has already completed onboarding
        const hasPreferences = await this.checkExistingPreferences();
        if (hasPreferences) {
            // User has already completed onboarding, redirect to profile
            window.location.href = '/profile';
            return;
        }

        this.setupEventListeners();
        this.updateProgressIndicator();
    }

    // Check if user already has preferences set
    async checkExistingPreferences() {
        try {
            // For demo purposes, we'll assume new users don't have preferences
            // In a real app, you'd check the database here
            return false;
        } catch (error) {
            console.error('Error checking preferences:', error);
            return false;
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Preference card clicks
        document.querySelectorAll('.preference-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const checkbox = card.querySelector('input[type="checkbox"]');
                const activity = card.dataset.activity;
                
                // Toggle checkbox
                checkbox.checked = !checkbox.checked;
                
                // Update visual state
                if (checkbox.checked) {
                    card.classList.add('selected');
                    if (!this.selectedActivities.includes(activity)) {
                        this.selectedActivities.push(activity);
                    }
                } else {
                    card.classList.remove('selected');
                    this.selectedActivities = this.selectedActivities.filter(a => a !== activity);
                }
                
                this.updateSelectedActivities();
            });
        });

        // Checkbox changes (for accessibility)
        document.querySelectorAll('.preference-card input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const card = checkbox.closest('.preference-card');
                const activity = card.dataset.activity;
                
                if (checkbox.checked) {
                    card.classList.add('selected');
                    if (!this.selectedActivities.includes(activity)) {
                        this.selectedActivities.push(activity);
                    }
                } else {
                    card.classList.remove('selected');
                    this.selectedActivities = this.selectedActivities.filter(a => a !== activity);
                }
                
                this.updateSelectedActivities();
            });
        });
    }

    // Move to next step
    nextStep() {
        if (this.currentStep < this.totalSteps) {
            // Validate current step
            if (!this.validateCurrentStep()) {
                return;
            }

            // Hide current step
            document.getElementById(`step-${this.currentStep}`).classList.remove('active');
            
            // Move to next step
            this.currentStep++;
            
            // Show next step
            document.getElementById(`step-${this.currentStep}`).classList.add('active');
            
            // Update progress
            this.updateProgressIndicator();
            
            // Handle step-specific actions
            this.handleStepEnter();
        }
    }

    // Move to previous step
    prevStep() {
        if (this.currentStep > 1) {
            // Hide current step
            document.getElementById(`step-${this.currentStep}`).classList.remove('active');
            
            // Move to previous step
            this.currentStep--;
            
            // Show previous step
            document.getElementById(`step-${this.currentStep}`).classList.add('active');
            
            // Update progress
            this.updateProgressIndicator();
        }
    }

    // Validate current step
    validateCurrentStep() {
        switch (this.currentStep) {
            case 1:
                return true; // Welcome step, no validation needed
            
            case 2:
                // Preferences step - at least one activity should be selected
                if (this.selectedActivities.length === 0) {
                    authManager.showMessage('Please select at least one activity to continue', 'warning');
                    return false;
                }
                return true;
            
            case 3:
                return true; // Features overview, no validation needed
            
            default:
                return true;
        }
    }

    // Handle entering a specific step
    handleStepEnter() {
        switch (this.currentStep) {
            case 4:
                // Final step - show selected activities
                this.displaySelectedActivities();
                break;
        }
    }

    // Update progress indicator
    updateProgressIndicator() {
        // Update step indicators
        for (let i = 1; i <= this.totalSteps; i++) {
            const stepEl = document.querySelector(`[data-step="${i}"]`);
            if (stepEl) {
                stepEl.classList.remove('active', 'completed');
                
                if (i < this.currentStep) {
                    stepEl.classList.add('completed');
                } else if (i === this.currentStep) {
                    stepEl.classList.add('active');
                }
            }
        }

        // Update progress lines
        document.querySelectorAll('.progress-line').forEach((line, index) => {
            line.classList.remove('completed');
            if (index < this.currentStep - 1) {
                line.classList.add('completed');
            }
        });
    }

    // Update selected activities display
    updateSelectedActivities() {
        // This will be used in the final step
        console.log('Selected activities:', this.selectedActivities);
    }

    // Display selected activities in final step
    displaySelectedActivities() {
        const container = document.getElementById('selected-activities');
        
        if (this.selectedActivities.length === 0) {
            container.innerHTML = '<p>No activities selected</p>';
            return;
        }

        // Create activity tags
        const activityIcons = {
            hiking: '🥾',
            concerts: '🎵',
            fishing: '🎣',
            camping: '🏕️',
            sports: '⚽',
            festivals: '🎪',
            picnics: '🧺',
            photography: '📸'
        };

        container.innerHTML = '';
        this.selectedActivities.forEach(activity => {
            const tag = document.createElement('div');
            tag.className = 'activity-tag';
            tag.innerHTML = `${activityIcons[activity] || '🎯'} ${activity.charAt(0).toUpperCase() + activity.slice(1)}`;
            container.appendChild(tag);
        });
    }

    // Complete onboarding
    async completeOnboarding() {
        try {
            // Save preferences
            await this.savePreferences();
            
            // Mark onboarding as complete
            localStorage.setItem('onboarding_completed', 'true');
            
            // Show success message
            authManager.showMessage('Welcome to EventPlanner! Your preferences have been saved. 🎉', 'success');
            
            // Redirect to profile after a short delay
            setTimeout(() => {
                window.location.href = '/profile';
            }, 2000);

        } catch (error) {
            console.error('Error completing onboarding:', error);
            authManager.showMessage('Error saving preferences. Please try again.', 'error');
        }
    }

    // Save user preferences
    async savePreferences() {
        try {
            // For demo purposes, we'll save to localStorage
            // In a real app, you'd save to the database via API
            const preferences = {
                user_id: this.currentUser.id,
                activities: this.selectedActivities,
                onboarding_completed: true,
                completed_at: new Date().toISOString()
            };

            localStorage.setItem('user_preferences', JSON.stringify(preferences));

            // Simulate API call
            console.log('Saving preferences:', preferences);

            // In a real app, you'd make this API call:
            /*
            const response = await fetch('/api/user/preferences', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(preferences)
            });

            if (!response.ok) {
                throw new Error('Failed to save preferences');
            }
            */

        } catch (error) {
            console.error('Error saving preferences:', error);
            throw error;
        }
    }

    // Skip onboarding (optional)
    skipOnboarding() {
        if (confirm('Are you sure you want to skip the setup? You can always set your preferences later in your profile.')) {
            localStorage.setItem('onboarding_completed', 'true');
            window.location.href = '/profile';
        }
    }
}

// Create global onboarding manager instance
const onboardingManager = new OnboardingManager();

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    const initOnboarding = () => {
        if (authManager.getCurrentUser()) {
            onboardingManager.init();
        } else {
            setTimeout(initOnboarding, 100);
        }
    };
    
    initOnboarding();
});

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'ArrowRight' || e.key === 'Enter') {
        if (onboardingManager.currentStep < onboardingManager.totalSteps) {
            onboardingManager.nextStep();
        }
    } else if (e.key === 'ArrowLeft') {
        if (onboardingManager.currentStep > 1) {
            onboardingManager.prevStep();
        }
    }
});
