// Profile page functionality
class ProfileManager {
    constructor() {
        this.currentUser = null;
        this.userProfile = null;
        this.filteredView = false;
    }

    // Initialize profile page
    async init() {
        this.currentUser = authManager.getCurrentUser();
        
        if (!this.currentUser) {
            window.location.href = '/';
            return;
        }

        await this.loadProfileData();
        this.setupEventListeners();
    }

    // Load all profile data
    async loadProfileData() {
        try {
            this.showLoading();
            
            // For demo purposes, we'll use mock data if API fails
            try {
                const response = await fetch(`/api/user/profile?user_id=${this.currentUser.id}`);
                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }
                
                this.userProfile = data;
            } catch (apiError) {
                console.warn('API not available, using mock data:', apiError);
                this.userProfile = this.getMockProfileData();
            }

            this.updateProfileUI();
            this.loadUserStats();
            this.loadSavedEvents();
            this.loadRecentAlerts();
            this.loadUserPreferences();

        } catch (error) {
            console.error('Error loading profile:', error);
            this.showError('Failed to load profile data');
        }
    }

    // Get mock data for demo
    getMockProfileData() {
        return {
            user: {
                id: this.currentUser.id,
                email: this.currentUser.email,
                created_at: new Date().toISOString()
            },
            stats: {
                total_events: 5,
                events_attended: 3,
                alerts_sent: 8
            },
            preferences: {
                activities: ['hiking', 'concerts', 'photography']
            },
            saved_events: [
                {
                    id: '1',
                    event_title: 'Mountain Hiking Adventure',
                    event_date: '2025-10-15',
                    event_location: 'Rocky Mountains',
                    event_type: 'hiking',
                    weather_status: 'sunny',
                    status: 'saved',
                    created_at: '2025-10-01T10:00:00Z'
                },
                {
                    id: '2',
                    event_title: 'Jazz Festival Downtown',
                    event_date: '2025-09-20',
                    event_location: 'City Center',
                    event_type: 'concerts',
                    weather_status: 'cloudy',
                    status: 'past',
                    created_at: '2025-09-15T14:30:00Z'
                },
                {
                    id: '3',
                    event_title: 'Photography Workshop',
                    event_date: '2025-10-25',
                    event_location: 'Art District',
                    event_type: 'photography',
                    weather_status: 'unknown',
                    status: 'saved',
                    created_at: '2025-10-02T09:15:00Z'
                }
            ],
            recent_alerts: [
                {
                    id: '1',
                    message: 'Weather alert: Rain expected for your hiking event on Oct 15th',
                    type: 'warning',
                    created_at: '2025-10-03T08:00:00Z',
                    read: false
                },
                {
                    id: '2',
                    message: 'New concert event matches your preferences!',
                    type: 'info',
                    created_at: '2025-10-02T16:30:00Z',
                    read: false
                },
                {
                    id: '3',
                    message: 'Your photography workshop is coming up in 3 days',
                    type: 'success',
                    created_at: '2025-10-01T12:00:00Z',
                    read: true
                }
            ]
        };
    }

    // Update profile UI elements
    updateProfileUI() {
        if (this.userProfile.user) {
            document.getElementById('profile-email').textContent = this.userProfile.user.email;
        }
    }

    // Load and display user stats
    loadUserStats() {
        const stats = this.userProfile.stats || {};
        
        document.getElementById('stat-total-events').textContent = stats.total_events || 0;
        document.getElementById('stat-events-attended').textContent = stats.events_attended || 0;
        document.getElementById('stat-alerts-sent').textContent = stats.alerts_sent || 0;
        
        // Calculate this month's events
        const thisMonth = this.calculateThisMonthEvents();
        document.getElementById('stat-this-month').textContent = thisMonth;
    }

    // Calculate events for current month
    calculateThisMonthEvents() {
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        
        return this.userProfile.saved_events.filter(event => {
            const eventDate = new Date(event.created_at);
            return eventDate.getMonth() === currentMonth && eventDate.getFullYear() === currentYear;
        }).length;
    }

    // Load and display saved events
    loadSavedEvents() {
        const container = document.getElementById('saved-events-container');
        const events = this.userProfile.saved_events || [];

        if (events.length === 0) {
            container.innerHTML = '<p>No saved events yet. <a href="/events">Plan your first event!</a></p>';
            return;
        }

        container.innerHTML = '';
        events.forEach(event => {
            const eventEl = this.createEventCard(event);
            container.appendChild(eventEl);
        });
    }

    // Create event card element
    createEventCard(event) {
        const eventEl = document.createElement('div');
        eventEl.className = 'event-card';
        
        const statusClass = event.status === 'past' ? 'status-past' : 'status-saved';
        const weatherIcon = this.getWeatherIcon(event.weather_status);
        const eventDate = new Date(event.event_date).toLocaleDateString();
        
        eventEl.innerHTML = `
            <div class="flex justify-between align-center">
                <div>
                    <div class="event-title">${event.event_title}</div>
                    <div class="event-date">📅 ${eventDate} • 📍 ${event.event_location}</div>
                    <div class="event-type">🏷️ ${event.event_type}</div>
                </div>
                <div class="text-right">
                    <div class="event-status ${statusClass}">
                        <span class="status-indicator"></span>
                        ${event.status}
                    </div>
                    <div class="weather-status">${weatherIcon} ${event.weather_status}</div>
                </div>
            </div>
        `;

        return eventEl;
    }

    // Get weather icon based on status
    getWeatherIcon(status) {
        const icons = {
            'sunny': '☀️',
            'cloudy': '☁️',
            'rainy': '🌧️',
            'stormy': '⛈️',
            'snowy': '❄️',
            'unknown': '❓'
        };
        return icons[status] || '❓';
    }

    // Load and display recent alerts
    loadRecentAlerts() {
        const container = document.getElementById('alerts-container');
        const alerts = this.userProfile.recent_alerts || [];

        if (alerts.length === 0) {
            container.innerHTML = '<p>No recent alerts</p>';
            return;
        }

        container.innerHTML = '';
        alerts.slice(0, 5).forEach(alert => {
            const alertEl = this.createAlertElement(alert);
            container.appendChild(alertEl);
        });
    }

    // Create alert element
    createAlertElement(alert) {
        const alertEl = document.createElement('div');
        alertEl.className = `alert alert-${alert.type}`;
        
        const date = new Date(alert.created_at).toLocaleDateString();
        const readStatus = alert.read ? '' : ' (New)';
        
        alertEl.innerHTML = `
            <div class="flex justify-between">
                <div>
                    <strong>${date}${readStatus}</strong><br>
                    ${alert.message}
                </div>
                <div>
                    ${alert.read ? '✅' : '🔔'}
                </div>
            </div>
        `;

        return alertEl;
    }

    // Load user preferences
    loadUserPreferences() {
        const preferences = this.userProfile.preferences || {};
        const activities = preferences.activities || [];

        // Check the appropriate checkboxes
        activities.forEach(activity => {
            const checkbox = document.getElementById(activity);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }

    // Save user preferences
    async savePreferences() {
        try {
            const formData = new FormData(document.getElementById('preferences-form'));
            const activities = formData.getAll('activities');

            // Update local data
            this.userProfile.preferences.activities = activities;

            // Show success message
            this.showPreferencesStatus('Preferences saved! 💾', 'success');

            // In a real app, you'd make an API call here:
            // await fetch('/api/user/preferences', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify({ user_id: this.currentUser.id, activities })
            // });

        } catch (error) {
            console.error('Error saving preferences:', error);
            this.showPreferencesStatus('Error saving preferences ❌', 'error');
        }
    }

    // Filter events by user preferences
    filterEventsByPreferences() {
        const userActivities = this.userProfile.preferences.activities || [];
        const allEvents = this.userProfile.saved_events || [];

        if (this.filteredView) {
            // Show all events
            this.loadSavedEvents();
            this.filteredView = false;
            document.getElementById('filter-events').textContent = '🔍 Filter by Preferences';
        } else {
            // Show filtered events
            const filteredEvents = allEvents.filter(event => 
                userActivities.includes(event.event_type)
            );

            const container = document.getElementById('saved-events-container');
            
            if (filteredEvents.length === 0) {
                container.innerHTML = '<p>No events match your preferences. Try updating your activity preferences!</p>';
            } else {
                container.innerHTML = '';
                filteredEvents.forEach(event => {
                    const eventEl = this.createEventCard(event);
                    container.appendChild(eventEl);
                });
            }

            this.filteredView = true;
            document.getElementById('filter-events').textContent = '📋 Show All Events';
        }
    }

    // Generate sample alert for demo
    async generateSampleAlert() {
        const sampleAlerts = [
            { message: 'Weather update: Perfect conditions for your upcoming hiking trip!', type: 'success' },
            { message: 'New outdoor concert added that matches your preferences', type: 'info' },
            { message: 'Rain warning for your picnic event tomorrow', type: 'warning' },
            { message: 'Your photography workshop starts in 2 hours', type: 'info' }
        ];

        const randomAlert = sampleAlerts[Math.floor(Math.random() * sampleAlerts.length)];
        
        // Add to recent alerts
        this.userProfile.recent_alerts.unshift({
            id: Date.now().toString(),
            ...randomAlert,
            created_at: new Date().toISOString(),
            read: false
        });

        // Update alerts count
        this.userProfile.stats.alerts_sent++;
        
        // Refresh UI
        this.loadRecentAlerts();
        this.loadUserStats();
        
        authManager.showMessage('Sample alert generated! 🔔', 'success');
    }

    // Setup event listeners
    setupEventListeners() {
        // Refresh profile button
        document.getElementById('refresh-profile').addEventListener('click', () => {
            this.loadProfileData();
        });

        // Preferences form
        document.getElementById('preferences-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.savePreferences();
        });

        // Filter events button
        document.getElementById('filter-events').addEventListener('click', () => {
            this.filterEventsByPreferences();
        });

        // Generate sample alert
        document.getElementById('generate-sample-alert').addEventListener('click', () => {
            this.generateSampleAlert();
        });

        // Check weather alerts
        document.getElementById('check-weather-alerts').addEventListener('click', () => {
            this.checkWeatherAlerts();
        });

        // Mark all alerts as read
        document.getElementById('mark-all-read').addEventListener('click', () => {
            this.markAllAlertsRead();
        });
    }

    // Check weather alerts for upcoming events
    checkWeatherAlerts() {
        const upcomingEvents = this.userProfile.saved_events.filter(event => {
            const eventDate = new Date(event.event_date);
            const today = new Date();
            return eventDate > today && event.status === 'saved';
        });

        if (upcomingEvents.length === 0) {
            authManager.showMessage('No upcoming events to check weather for', 'info');
            return;
        }

        // Simulate weather check
        upcomingEvents.forEach(event => {
            const weatherConditions = ['sunny', 'cloudy', 'rainy', 'stormy'];
            const randomWeather = weatherConditions[Math.floor(Math.random() * weatherConditions.length)];
            
            event.weather_status = randomWeather;
            
            if (randomWeather === 'rainy' || randomWeather === 'stormy') {
                this.userProfile.recent_alerts.unshift({
                    id: Date.now().toString() + Math.random(),
                    message: `Weather alert: ${randomWeather} conditions expected for "${event.event_title}" on ${new Date(event.event_date).toLocaleDateString()}`,
                    type: 'warning',
                    created_at: new Date().toISOString(),
                    read: false
                });
            }
        });

        this.loadSavedEvents();
        this.loadRecentAlerts();
        authManager.showMessage('Weather check complete! ⛅', 'success');
    }

    // Mark all alerts as read
    markAllAlertsRead() {
        this.userProfile.recent_alerts.forEach(alert => {
            alert.read = true;
        });

        this.loadRecentAlerts();
        authManager.showMessage('All alerts marked as read ✅', 'success');
    }

    // Show preferences status
    showPreferencesStatus(message, type) {
        const statusEl = document.getElementById('preferences-status');
        statusEl.textContent = message;
        statusEl.className = type === 'success' ? 'text-success' : 'text-error';
        
        setTimeout(() => {
            statusEl.textContent = '';
            statusEl.className = '';
        }, 3000);
    }

    // Show loading state
    showLoading() {
        const containers = ['saved-events-container', 'alerts-container'];
        containers.forEach(id => {
            const container = document.getElementById(id);
            if (container) {
                container.innerHTML = '<div class="loading"><div class="spinner"></div>Loading...</div>';
            }
        });
    }

    // Show error message
    showError(message) {
        authManager.showMessage(message, 'error');
    }
}

// Initialize profile manager when page loads
document.addEventListener('DOMContentLoaded', function() {
    const profileManager = new ProfileManager();
    
    // Wait for auth to be ready
    const initProfile = () => {
        if (authManager.getCurrentUser()) {
            profileManager.init();
        } else {
            setTimeout(initProfile, 100);
        }
    };
    
    initProfile();
});
