// Authentication utilities
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.supabase = null;
    }

    // Initialize Supabase client (will be set from main app)
    init(supabaseClient) {
        this.supabase = supabaseClient;
        this.checkAuthState();
    }

    // Check current authentication state
    async checkAuthState() {
        try {
            const { data: { user } } = await this.supabase.auth.getUser();
            this.currentUser = user;
            this.updateUI();
        } catch (error) {
            console.error('Auth check error:', error);
        }
    }

    // Sign up new user
    async signUp(email, password) {
        try {
            const { data, error } = await this.supabase.auth.signUp({
                email: email,
                password: password
            });

            if (error) throw error;

            if (data.user) {
                // Create user profile
                await this.createUserProfile(data.user);
                this.showMessage('Account created successfully! Please check your email to verify.', 'success');

                // For new users, clear any existing onboarding data
                localStorage.removeItem('onboarding_completed');
                localStorage.removeItem('user_preferences');

                return true;
            }
        } catch (error) {
            this.showMessage(error.message, 'error');
            return false;
        }
    }

    // Sign in user
    async signIn(email, password) {
        try {
            const { data, error } = await this.supabase.auth.signInWithPassword({
                email: email,
                password: password
            });

            if (error) throw error;

            this.currentUser = data.user;
            this.updateUI();
            this.showMessage('Signed in successfully!', 'success');

            // Check if user needs onboarding
            await this.checkOnboardingStatus();

            return true;
        } catch (error) {
            this.showMessage(error.message, 'error');
            return false;
        }
    }

    // Check if user needs onboarding
    async checkOnboardingStatus() {
        try {
            // Check if onboarding was completed
            const onboardingCompleted = localStorage.getItem('onboarding_completed');

            if (!onboardingCompleted) {
                // Check if user has preferences set
                const userPrefs = localStorage.getItem('user_preferences');

                if (!userPrefs) {
                    // New user, redirect to onboarding
                    setTimeout(() => {
                        window.location.href = '/onboarding';
                    }, 1000);
                }
            }
        } catch (error) {
            console.error('Error checking onboarding status:', error);
        }
    }

    // Sign out user
    async signOut() {
        try {
            const { error } = await this.supabase.auth.signOut();
            if (error) throw error;

            this.currentUser = null;
            this.updateUI();
            this.showMessage('Signed out successfully!', 'success');
            window.location.href = '/';
        } catch (error) {
            this.showMessage(error.message, 'error');
        }
    }

    // Create user profile in database
    async createUserProfile(user) {
        try {
            const { error } = await this.supabase
                .from('users')
                .insert([
                    {
                        id: user.id,
                        email: user.email,
                        created_at: new Date().toISOString()
                    }
                ]);

            if (error) throw error;

            // Initialize user stats
            await this.supabase
                .from('stats')
                .insert([
                    {
                        user_id: user.id,
                        total_events: 0,
                        alerts_sent: 0,
                        events_attended: 0
                    }
                ]);

        } catch (error) {
            console.error('Profile creation error:', error);
        }
    }

    // Update UI based on auth state
    updateUI() {
        const authElements = document.querySelectorAll('[data-auth]');
        const noAuthElements = document.querySelectorAll('[data-no-auth]');

        if (this.currentUser) {
            authElements.forEach(el => el.style.display = 'block');
            noAuthElements.forEach(el => el.style.display = 'none');
            
            // Update user info displays
            const userEmailElements = document.querySelectorAll('[data-user-email]');
            userEmailElements.forEach(el => el.textContent = this.currentUser.email);
        } else {
            authElements.forEach(el => el.style.display = 'none');
            noAuthElements.forEach(el => el.style.display = 'block');
        }
    }

    // Show message to user
    showMessage(message, type = 'info') {
        const messageContainer = document.getElementById('message-container');
        if (!messageContainer) return;

        const messageEl = document.createElement('div');
        messageEl.className = `alert alert-${type}`;
        messageEl.textContent = message;

        messageContainer.appendChild(messageEl);

        // Remove message after 5 seconds
        setTimeout(() => {
            messageEl.remove();
        }, 5000);
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if user is authenticated
    isAuthenticated() {
        return this.currentUser !== null;
    }
}

// Create global auth manager instance
const authManager = new AuthManager();

// Auth form handlers
document.addEventListener('DOMContentLoaded', function() {
    // Sign up form
    const signupForm = document.getElementById('signup-form');
    if (signupForm) {
        signupForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            const email = document.getElementById('signup-email').value;
            const password = document.getElementById('signup-password').value;
            
            if (await authManager.signUp(email, password)) {
                signupForm.reset();
            }
        });
    }

    // Sign in form
    const signinForm = document.getElementById('signin-form');
    if (signinForm) {
        signinForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            const email = document.getElementById('signin-email').value;
            const password = document.getElementById('signin-password').value;
            
            if (await authManager.signIn(email, password)) {
                window.location.href = '/profile';
            }
        });
    }

    // Sign out button
    const signoutBtn = document.getElementById('signout-btn');
    if (signoutBtn) {
        signoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            authManager.signOut();
        });
    }
});
