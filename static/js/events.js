// Event planning functionality
class EventManager {
    constructor() {
        this.currentUser = null;
        this.userPreferences = [];
        this.eventTemplates = {
            hiking: {
                title: 'Weekend Mountain Hike',
                type: 'hiking',
                description: 'A refreshing hike through scenic mountain trails with beautiful views.',
                time: '08:00'
            },
            picnic: {
                title: 'Family Picnic in the Park',
                type: 'picnics',
                description: 'A relaxing family gathering with food, games, and outdoor fun.',
                time: '12:00'
            },
            concert: {
                title: 'Outdoor Music Concert',
                type: 'concerts',
                description: 'Live music performance under the stars with great atmosphere.',
                time: '19:00'
            },
            photography: {
                title: 'Photography Walk',
                type: 'photography',
                description: 'Capture beautiful moments and scenery in a guided photo session.',
                time: '10:00'
            }
        };
    }

    // Initialize event manager
    async init() {
        this.currentUser = authManager.getCurrentUser();
        
        if (!this.currentUser) {
            window.location.href = '/';
            return;
        }

        await this.loadUserPreferences();
        this.setupEventListeners();
        this.loadRecommendations();
        this.loadRecentEvents();
        this.setDefaultDate();
    }

    // Set default date to tomorrow
    setDefaultDate() {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        document.getElementById('event-date').value = tomorrow.toISOString().split('T')[0];
    }

    // Load user preferences for recommendations
    async loadUserPreferences() {
        try {
            // For demo, we'll use mock preferences
            this.userPreferences = ['hiking', 'concerts', 'photography'];
        } catch (error) {
            console.error('Error loading preferences:', error);
            this.userPreferences = [];
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Event form submission
        document.getElementById('event-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveEvent();
        });

        // Check weather button
        document.getElementById('check-weather').addEventListener('click', () => {
            this.checkWeather();
        });

        // Template buttons
        document.querySelectorAll('.template-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const template = e.target.dataset.template;
                this.applyTemplate(template);
            });
        });

        // Plan another event button
        document.getElementById('plan-another').addEventListener('click', () => {
            this.closeSuccessModal();
            this.resetForm();
        });

        // Auto-check weather when date/location changes
        document.getElementById('event-date').addEventListener('change', () => {
            this.checkWeather();
        });

        document.getElementById('event-location').addEventListener('blur', () => {
            this.checkWeather();
        });
    }

    // Apply event template
    applyTemplate(templateName) {
        const template = this.eventTemplates[templateName];
        if (!template) return;

        document.getElementById('event-title').value = template.title;
        document.getElementById('event-type').value = template.type;
        document.getElementById('event-description').value = template.description;
        document.getElementById('event-time').value = template.time;

        // Show success message
        authManager.showMessage(`Template applied: ${template.title} 📋`, 'success');
    }

    // Save event
    async saveEvent() {
        try {
            const formData = new FormData(document.getElementById('event-form'));
            
            const eventData = {
                title: formData.get('title'),
                date: formData.get('date'),
                time: formData.get('time'),
                location: formData.get('location'),
                type: formData.get('type'),
                description: formData.get('description'),
                weather_alerts: formData.get('weather_alerts') === 'on'
            };

            // Validate required fields
            if (!eventData.title || !eventData.date || !eventData.location || !eventData.type) {
                authManager.showMessage('Please fill in all required fields', 'error');
                return;
            }

            // For demo purposes, we'll simulate saving
            console.log('Saving event:', eventData);

            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Show success modal
            this.showSuccessModal();

            // Add to recent events (mock)
            this.addToRecentEvents(eventData);

            // Generate weather alert if needed
            if (eventData.weather_alerts) {
                this.generateWeatherAlert(eventData);
            }

        } catch (error) {
            console.error('Error saving event:', error);
            authManager.showMessage('Error saving event. Please try again.', 'error');
        }
    }

    // Add event to recent events display
    addToRecentEvents(eventData) {
        const container = document.getElementById('recent-events-container');
        
        // Create event card
        const eventCard = document.createElement('div');
        eventCard.className = 'event-card';
        
        const eventDate = new Date(eventData.date).toLocaleDateString();
        
        eventCard.innerHTML = `
            <div class="flex justify-between align-center">
                <div>
                    <div class="event-title">${eventData.title}</div>
                    <div class="event-date">📅 ${eventDate} • 📍 ${eventData.location}</div>
                    <div class="event-type">🏷️ ${eventData.type}</div>
                </div>
                <div class="text-right">
                    <div class="event-status status-saved">
                        <span class="status-indicator"></span>
                        saved
                    </div>
                    <div class="weather-status">❓ checking...</div>
                </div>
            </div>
        `;

        // Add to top of container
        if (container.children.length === 0 || container.children[0].classList.contains('loading')) {
            container.innerHTML = '';
        }
        container.insertBefore(eventCard, container.firstChild);
    }

    // Generate weather alert for event
    generateWeatherAlert(eventData) {
        const weatherConditions = ['sunny', 'cloudy', 'rainy', 'stormy'];
        const randomWeather = weatherConditions[Math.floor(Math.random() * weatherConditions.length)];
        
        if (randomWeather === 'rainy' || randomWeather === 'stormy') {
            setTimeout(() => {
                authManager.showMessage(
                    `Weather Alert: ${randomWeather} conditions expected for "${eventData.title}" on ${new Date(eventData.date).toLocaleDateString()}`,
                    'warning'
                );
            }, 2000);
        }
    }

    // Check weather for event
    checkWeather() {
        const date = document.getElementById('event-date').value;
        const location = document.getElementById('event-location').value;

        if (!date || !location) {
            document.getElementById('weather-container').innerHTML = 
                '<p>Please enter both date and location to check weather</p>';
            return;
        }

        // Show loading
        document.getElementById('weather-container').innerHTML = 
            '<div class="loading"><div class="spinner"></div>Checking weather...</div>';

        // Simulate weather API call
        setTimeout(() => {
            this.displayWeatherForecast(date, location);
        }, 1500);
    }

    // Display weather forecast (mock data)
    displayWeatherForecast(date, location) {
        const weatherConditions = [
            { condition: 'Sunny', icon: '☀️', temp: '75°F', humidity: '45%', wind: '8 mph' },
            { condition: 'Partly Cloudy', icon: '⛅', temp: '68°F', humidity: '60%', wind: '12 mph' },
            { condition: 'Rainy', icon: '🌧️', temp: '62°F', humidity: '85%', wind: '15 mph' },
            { condition: 'Cloudy', icon: '☁️', temp: '70°F', humidity: '70%', wind: '10 mph' }
        ];

        const randomWeather = weatherConditions[Math.floor(Math.random() * weatherConditions.length)];
        const eventDate = new Date(date).toLocaleDateString();

        const weatherHTML = `
            <div class="weather-card">
                <div class="flex justify-between align-center">
                    <div>
                        <h4>${location}</h4>
                        <p>${eventDate}</p>
                    </div>
                    <div class="weather-icon">${randomWeather.icon}</div>
                </div>
                
                <div class="weather-details">
                    <div class="weather-item">
                        <div><strong>${randomWeather.condition}</strong></div>
                        <div>Condition</div>
                    </div>
                    <div class="weather-item">
                        <div><strong>${randomWeather.temp}</strong></div>
                        <div>Temperature</div>
                    </div>
                    <div class="weather-item">
                        <div><strong>${randomWeather.humidity}</strong></div>
                        <div>Humidity</div>
                    </div>
                    <div class="weather-item">
                        <div><strong>${randomWeather.wind}</strong></div>
                        <div>Wind Speed</div>
                    </div>
                </div>
                
                ${randomWeather.condition === 'Rainy' ? 
                    '<div class="alert alert-warning mt-1">⚠️ Rain expected - consider indoor alternatives or bring rain gear!</div>' : 
                    '<div class="alert alert-success mt-1">✅ Great weather for outdoor activities!</div>'
                }
            </div>
        `;

        document.getElementById('weather-container').innerHTML = weatherHTML;
    }

    // Load personalized recommendations
    loadRecommendations() {
        const recommendations = [
            {
                title: 'Photography Workshop in Botanical Gardens',
                reason: 'Matches your photography interest',
                type: 'photography',
                preferred: this.userPreferences.includes('photography')
            },
            {
                title: 'Sunset Hiking Trail',
                reason: 'Perfect for hiking enthusiasts',
                type: 'hiking',
                preferred: this.userPreferences.includes('hiking')
            },
            {
                title: 'Outdoor Jazz Concert Series',
                reason: 'Based on your music preferences',
                type: 'concerts',
                preferred: this.userPreferences.includes('concerts')
            },
            {
                title: 'Weekend Farmers Market',
                reason: 'Great for family outings',
                type: 'other',
                preferred: false
            }
        ];

        const container = document.getElementById('recommendations-container');
        container.innerHTML = '';

        // Sort by preference
        recommendations.sort((a, b) => b.preferred - a.preferred);

        recommendations.forEach(rec => {
            const recCard = document.createElement('div');
            recCard.className = `recommendation-card ${rec.preferred ? 'preferred' : ''}`;
            
            recCard.innerHTML = `
                <div class="recommendation-title">
                    ${rec.preferred ? '⭐ ' : ''}${rec.title}
                </div>
                <div class="recommendation-reason">${rec.reason}</div>
                <button class="btn btn-secondary mt-1" onclick="eventManager.useRecommendation('${rec.title}', '${rec.type}')">
                    Use This Idea
                </button>
            `;

            container.appendChild(recCard);
        });
    }

    // Use recommendation to fill form
    useRecommendation(title, type) {
        document.getElementById('event-title').value = title;
        document.getElementById('event-type').value = type;
        
        authManager.showMessage('Recommendation applied! 💡', 'success');
    }

    // Load recent events
    loadRecentEvents() {
        // Mock recent events
        const recentEvents = [
            {
                title: 'Mountain Photography Hike',
                date: '2025-09-28',
                location: 'Rocky Mountain National Park',
                type: 'photography',
                status: 'past'
            },
            {
                title: 'Jazz in the Park',
                date: '2025-09-25',
                location: 'Central Park',
                type: 'concerts',
                status: 'past'
            }
        ];

        const container = document.getElementById('recent-events-container');
        
        if (recentEvents.length === 0) {
            container.innerHTML = '<p>No recent events. Start planning your first event!</p>';
            return;
        }

        container.innerHTML = '';
        recentEvents.forEach(event => {
            const eventCard = document.createElement('div');
            eventCard.className = 'event-card';
            
            const eventDate = new Date(event.date).toLocaleDateString();
            
            eventCard.innerHTML = `
                <div class="flex justify-between align-center">
                    <div>
                        <div class="event-title">${event.title}</div>
                        <div class="event-date">📅 ${eventDate} • 📍 ${event.location}</div>
                        <div class="event-type">🏷️ ${event.type}</div>
                    </div>
                    <div class="text-right">
                        <div class="event-status status-${event.status}">
                            <span class="status-indicator"></span>
                            ${event.status}
                        </div>
                    </div>
                </div>
            `;

            container.appendChild(eventCard);
        });
    }

    // Show success modal
    showSuccessModal() {
        document.getElementById('success-modal').style.display = 'flex';
    }

    // Close success modal
    closeSuccessModal() {
        document.getElementById('success-modal').style.display = 'none';
    }

    // Reset form
    resetForm() {
        document.getElementById('event-form').reset();
        this.setDefaultDate();
        document.getElementById('weather-container').innerHTML = 
            '<p>Select a date and location to check weather forecast</p>';
    }
}

// Create global instance
const eventManager = new EventManager();

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    const initEvents = () => {
        if (authManager.getCurrentUser()) {
            eventManager.init();
        } else {
            setTimeout(initEvents, 100);
        }
    };
    
    initEvents();
});
