# 🌤️ EventPlanner - User Experience & Personalization

A complete event planning application with weather integration, user personalization, and real-time alerts. Built for hackathon with beginner-friendly technologies.

## 🚀 Features

### ✅ Completed Features

#### **User Authentication & Profiles**
- 🔐 Secure email/password authentication via Supabase
- 👤 User profile management with stats dashboard
- 📊 Activity tracking (total events, alerts, attendance)
- 🎯 Personalized activity preferences

#### **Event Planning & Management**
- 📅 Comprehensive event creation form
- 🌤️ Weather forecast integration (mock API)
- 💾 Save/favorite events functionality
- 🏷️ Event categorization by activity type
- 📋 Event history and status tracking

#### **Personalization System**
- 🎯 Activity preference selection (hiking, concerts, fishing, etc.)
- 💡 Personalized event recommendations
- 🔍 Filter events by user preferences
- ⚡ Quick event templates

#### **Alert & Notification System**
- 🔔 Weather alerts for upcoming events
- 📱 Real-time notification display
- ✅ Mark alerts as read functionality
- 🌧️ Automatic weather risk warnings

#### **Onboarding Experience**
- 🎉 4-step guided onboarding for new users
- 🎯 Interactive preference selection
- 📱 Feature walkthrough
- ✨ Smooth progress indicators

## 🛠️ Technology Stack

- **Backend**: Python Flask
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Styling**: Custom CSS with modern design
- **Icons**: Unicode emojis for universal compatibility

## 📁 Project Structure

```
EventPlanner/
├── app.py                 # Flask application
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
├── SUPABASE_SETUP.md     # Database setup guide
├── README.md             # This file
├── static/
│   ├── css/
│   │   └── style.css     # Main stylesheet
│   └── js/
│       ├── auth.js       # Authentication logic
│       ├── profile.js    # Profile page functionality
│       ├── events.js     # Event planning features
│       └── onboarding.js # Onboarding flow
└── templates/
    ├── base.html         # Base template
    ├── index.html        # Home/login page
    ├── profile.html      # User profile dashboard
    ├── events.html       # Event planning page
    └── onboarding.html   # New user onboarding
```

## 🚀 Quick Start

### 1. Setup Environment

```bash
# Clone or download the project
cd EventPlanner

# Install Python dependencies
pip install -r requirements.txt

# Copy environment template
cp .env.example .env
```

### 2. Configure Supabase

1. **Create Supabase Account**: Visit [supabase.com](https://supabase.com)
2. **Create New Project**: Follow the setup wizard
3. **Get Credentials**: Copy your Project URL and API Key
4. **Update .env**: Add your Supabase credentials
5. **Setup Database**: Follow instructions in `SUPABASE_SETUP.md`

### 3. Run the Application

```bash
# Start Flask development server
python app.py

# Open browser to http://localhost:5000
```

## 📋 Testing Guide

### Test Scenario 1: New User Registration & Onboarding

1. **Visit Homepage**: Go to `http://localhost:5000`
2. **Create Account**: Use the signup form with a valid email
3. **Complete Onboarding**: 
   - Step 1: Welcome screen
   - Step 2: Select activity preferences (try selecting 3-4 activities)
   - Step 3: Feature overview
   - Step 4: Completion summary
4. **Verify Redirect**: Should redirect to profile page

### Test Scenario 2: Profile Management

1. **View Profile Stats**: Check the stats dashboard shows correct numbers
2. **Update Preferences**: 
   - Change activity selections
   - Click "Save Preferences"
   - Verify success message
3. **Test Quick Actions**:
   - Generate sample alert
   - Check weather alerts
   - Mark alerts as read

### Test Scenario 3: Event Planning

1. **Navigate to Events**: Click "Plan Event" or visit `/events`
2. **Use Template**: Click a quick template button
3. **Fill Event Form**:
   - Enter event title, date, location
   - Select event type
   - Add description
4. **Check Weather**: Click "Check Weather" button
5. **Save Event**: Submit the form
6. **Verify Success**: Check success modal appears

### Test Scenario 4: Personalization Features

1. **Filter Events**: In profile, click "Filter by Preferences"
2. **View Recommendations**: Check events page recommendations
3. **Test Alerts**: Generate weather alerts for saved events
4. **Check Integration**: Verify events appear in profile

## 🐛 Troubleshooting

### Common Issues

#### **"Supabase not configured" Error**
- **Cause**: Missing or incorrect Supabase credentials
- **Solution**: Check `.env` file has correct `SUPABASE_URL` and `SUPABASE_KEY`

#### **Database Connection Errors**
- **Cause**: Database tables not created or RLS policies missing
- **Solution**: Follow `SUPABASE_SETUP.md` completely

#### **Authentication Not Working**
- **Cause**: Supabase auth not properly configured
- **Solution**: 
  1. Check Supabase dashboard → Authentication → Settings
  2. Ensure email auth is enabled
  3. Set Site URL to `http://localhost:5000`

#### **Events Not Saving**
- **Cause**: API endpoints returning errors
- **Solution**: Check browser console for errors, verify database tables exist

#### **Onboarding Loop**
- **Cause**: localStorage not clearing properly
- **Solution**: Clear browser localStorage or use incognito mode

### Debug Mode

Enable debug mode by setting in `.env`:
```
FLASK_ENV=development
```

Check browser console (F12) for JavaScript errors and network requests.

## 🎯 Demo Script

### For Hackathon Presentation (5 minutes)

1. **Introduction** (30 seconds)
   - "EventPlanner helps users plan perfect events with weather integration and personalization"

2. **New User Experience** (90 seconds)
   - Show signup process
   - Walk through onboarding flow
   - Highlight preference selection

3. **Event Planning** (90 seconds)
   - Create a new event using template
   - Show weather integration
   - Demonstrate saving functionality

4. **Personalization** (90 seconds)
   - Show profile dashboard with stats
   - Filter events by preferences
   - Generate and view alerts

5. **Conclusion** (30 seconds)
   - Highlight key features: personalization, weather integration, user-friendly design

## 🔧 Development Notes

### Mock Data
- Weather API calls are simulated for demo purposes
- User stats are calculated from mock data
- Alerts are generated randomly for demonstration

### Real Implementation
For production deployment:
1. Replace mock weather API with real service (OpenWeatherMap, etc.)
2. Implement proper Supabase API integration
3. Add email notifications for alerts
4. Implement real-time subscriptions
5. Add data validation and error handling

### Browser Compatibility
- Tested on Chrome, Firefox, Safari
- Uses modern CSS features (Grid, Flexbox)
- JavaScript ES6+ features used

## 📝 License

This project is created for educational/hackathon purposes. Feel free to use and modify as needed.

## 🤝 Contributing

This is a hackathon project, but suggestions and improvements are welcome!

---

**Built with ❤️ for the hackathon by Member 5**
