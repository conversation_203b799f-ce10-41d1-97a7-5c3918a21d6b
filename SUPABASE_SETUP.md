# 🚀 Supabase Setup Guide

## Step 1: Create Supabase Account

1. Go to [https://supabase.com](https://supabase.com)
2. Click "Start your project" 
3. Sign up with GitHub, Google, or email
4. Create a new project:
   - Project name: `event-planner-app`
   - Database password: (choose a strong password)
   - Region: Choose closest to your location

## Step 2: Get Your Project Credentials

1. In your Supabase dashboard, go to Settings → API
2. Copy your:
   - **Project URL** (looks like: `https://ltitsevhrmbrpnzaarmg.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Vhk4P9QodhDVfXo5mUapvJBJF77tAVRUmzsx1x9UJdo`)

## Step 3: Configure Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` file with your credentials:
   ```
   SUPABASE_URL=https://ltitsevhrmbrpnzaarmg.supabase.co
   SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Vhk4P9QodhDVfXo5mUapvJBJF77tAVRUmzsx1x9UJdo
   FLASK_SECRET_KEY=your-random-secret-key
   FLASK_ENV=development
   ```

## Step 4: Create Database Tables

Go to your Supabase dashboard → SQL Editor and run these commands:

### 1. Users Table (extends Supabase auth.users)
```sql
CREATE TABLE users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS (Row Level Security)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own data
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);
```

### 2. User Preferences Table
```sql
CREATE TABLE preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    activities TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE preferences ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own preferences" ON preferences
    FOR ALL USING (auth.uid() = user_id);
```

### 3. Saved Events Table
```sql
CREATE TABLE saved_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    event_title TEXT NOT NULL,
    event_date DATE,
    event_location TEXT,
    event_type TEXT,
    weather_status TEXT DEFAULT 'unknown',
    status TEXT DEFAULT 'saved' CHECK (status IN ('saved', 'past', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE saved_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own events" ON saved_events
    FOR ALL USING (auth.uid() = user_id);
```

### 4. User Stats Table
```sql
CREATE TABLE stats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    total_events INTEGER DEFAULT 0,
    events_attended INTEGER DEFAULT 0,
    alerts_sent INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE stats ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own stats" ON stats
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own stats" ON stats
    FOR UPDATE USING (auth.uid() = user_id);
```

### 5. Alerts Table
```sql
CREATE TABLE alerts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'success', 'error')),
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE alerts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own alerts" ON alerts
    FOR ALL USING (auth.uid() = user_id);
```

### 6. Create Indexes for Performance
```sql
CREATE INDEX idx_preferences_user_id ON preferences(user_id);
CREATE INDEX idx_saved_events_user_id ON saved_events(user_id);
CREATE INDEX idx_stats_user_id ON stats(user_id);
CREATE INDEX idx_alerts_user_id ON alerts(user_id);
CREATE INDEX idx_alerts_created_at ON alerts(created_at DESC);
```

## Step 5: Enable Authentication

1. Go to Authentication → Settings
2. Enable email authentication
3. Configure email templates (optional)
4. Set site URL to `http://localhost:5000` for development

## Step 6: Test Connection

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the Flask app:
   ```bash
   python app.py
   ```

3. Visit `http://localhost:5000` and try creating an account

## Common Issues & Solutions

### Issue: "Invalid API key"
- Double-check your SUPABASE_KEY in .env
- Make sure you're using the "anon" key, not the "service_role" key

### Issue: "Cross-origin request blocked"
- Add your domain to Supabase → Authentication → Settings → Site URL

### Issue: "Row Level Security policy violation"
- Make sure RLS policies are created correctly
- Check that auth.uid() matches the user_id in your queries

### Issue: Tables not found
- Verify all SQL commands ran successfully
- Check the Tables view in Supabase dashboard

## Next Steps

Once Supabase is set up:
1. Test user registration and login
2. Verify data is being saved to tables
3. Check that RLS policies are working
4. Move on to building the profile page features

## Useful Supabase Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [Python Client Documentation](https://supabase.com/docs/reference/python/introduction)
