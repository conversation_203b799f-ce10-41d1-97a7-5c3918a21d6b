# ⚡ Quick Start Guide - EventPlanner

## 🚀 Get Running in 5 Minutes

### Step 1: Setup (2 minutes)
```bash
# Install dependencies
pip install -r requirements.txt

# Copy environment file
cp .env.example .env

# (Optional) Add Supabase credentials to .env
# The app works with mock data without Supa<PERSON>
```

### Step 2: Start App (1 minute)
```bash
# Easy startup with checks
python start.py

# OR standard Flask startup
python app.py
```

### Step 3: Test Features (2 minutes)
1. **Visit**: `http://localhost:5000`
2. **Create Account**: Use any email/password
3. **Complete Onboarding**: Select 2-3 activities
4. **Plan Event**: Use "Weekend Hike" template
5. **Check Profile**: View stats and saved events

## 🎯 Demo Ready Features

### ✅ What Works Right Now
- 🔐 **User Authentication** (mock mode)
- 🎯 **Onboarding Flow** (4 interactive steps)
- 👤 **Profile Dashboard** (stats, preferences, events)
- 📅 **Event Planning** (templates, weather, saving)
- 🔔 **Alert System** (weather warnings, notifications)
- 📱 **Responsive Design** (mobile-friendly)

### 🎬 Perfect for Demo
- **New User Experience**: Complete onboarding flow
- **Personalization**: Preference-based recommendations
- **Weather Integration**: Mock weather API with alerts
- **Modern UI**: Clean, professional design

## 🐛 Troubleshooting

### App Won't Start?
```bash
# Check Python version (3.7+)
python --version

# Install missing packages
pip install flask python-dotenv supabase requests

# Run from correct directory
ls app.py  # Should exist
```

### Features Not Working?
- **Check Browser Console**: Press F12 → Console tab
- **Clear Browser Data**: Use incognito mode
- **Restart App**: Ctrl+C then `python start.py`

### Need Real Database?
1. **Follow**: `SUPABASE_SETUP.md`
2. **Add Credentials**: Update `.env` file
3. **Restart App**: `python start.py`

## 📋 Quick Test Checklist

- [ ] App starts without errors
- [ ] Can create account
- [ ] Onboarding completes
- [ ] Can plan event
- [ ] Profile shows data
- [ ] Alerts generate
- [ ] Mobile responsive

## 🎉 You're Ready!

Your EventPlanner app is now running with:
- ✨ Complete user experience flow
- 🎯 Personalization features
- 🌤️ Weather integration
- 📊 Activity tracking
- 🔔 Alert system

**Perfect for your hackathon demo!** 🚀

---

**Need help?** Check `TESTING_GUIDE.md` for detailed testing scenarios.
