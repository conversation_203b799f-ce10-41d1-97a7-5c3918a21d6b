#!/usr/bin/env python3
"""
EventPlanner Startup Script
Simplified startup with environment checking and helpful messages
"""

import os
import sys
from pathlib import Path

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking requirements...")
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("📝 Please copy .env.example to .env and add your Supabase credentials")
        print("   cp .env.example .env")
        return False
    
    # Check if requirements are installed
    try:
        import flask
        import supabase
        import dotenv
        print("✅ All Python packages installed")
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("📦 Please install requirements:")
        print("   pip install -r requirements.txt")
        return False
    
    # Check .env content
    from dotenv import load_dotenv
    load_dotenv()
    
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_KEY')
    
    if not supabase_url or not supabase_key:
        print("⚠️  Supabase credentials not configured")
        print("🔧 The app will run in demo mode with mock data")
        print("📖 See SUPABASE_SETUP.md for database setup instructions")
    else:
        print("✅ Supabase credentials found")
    
    return True

def print_startup_info():
    """Print helpful startup information"""
    print("\n" + "="*60)
    print("🌤️  EventPlanner - User Experience & Personalization")
    print("="*60)
    print("🚀 Starting Flask development server...")
    print("📱 Open your browser to: http://localhost:5000")
    print("\n📋 Quick Test Guide:")
    print("   1. Create account with any email/password")
    print("   2. Complete onboarding (select activities)")
    print("   3. Plan an event using templates")
    print("   4. Check your profile and stats")
    print("\n🐛 Troubleshooting:")
    print("   - Check browser console (F12) for errors")
    print("   - See TESTING_GUIDE.md for detailed testing")
    print("   - See README.md for setup instructions")
    print("\n⚡ Demo Tips:")
    print("   - Use 'Weekend Hike' template for quick demo")
    print("   - Test weather alerts and preferences")
    print("   - Show mobile responsiveness")
    print("="*60)

def main():
    """Main startup function"""
    print("🎯 EventPlanner Startup")
    print("-" * 30)
    
    # Check if we're in the right directory
    if not os.path.exists('app.py'):
        print("❌ app.py not found!")
        print("📁 Please run this script from the EventPlanner directory")
        sys.exit(1)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed!")
        print("🔧 Please fix the issues above and try again")
        sys.exit(1)
    
    # Print startup info
    print_startup_info()
    
    # Start the Flask app
    try:
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n\n👋 EventPlanner stopped. Thanks for testing!")
    except Exception as e:
        print(f"\n❌ Error starting app: {e}")
        print("🔧 Check the error message above and try again")
        sys.exit(1)

if __name__ == "__main__":
    main()
