from flask import Flask, render_template, request, jsonify, session, redirect, url_for
from supabase import create_client, Client
import os
from dotenv import load_dotenv
import json
from datetime import datetime, timedelta

# Load environment variables
load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv('FLASK_SECRET_KEY', 'your-secret-key-here')

# Supabase configuration
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Check if we have real Supabase credentials or demo mode
DEMO_MODE = (not SUPABASE_URL or not SUPABASE_KEY or
             SUPABASE_URL == "https://demo.supabase.co" or
             SUPABASE_KEY == "demo_key_for_development")

if DEMO_MODE:
    print("🎯 Running in DEMO MODE with mock data")
    print("For production, set up real Supabase credentials in .env")
    supabase = None
else:
    print("🔗 Connecting to Supabase...")
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Routes
@app.route('/')
def index():
    """Home page with auth forms"""
    return render_template('index.html')

@app.route('/profile')
def profile():
    """User profile page"""
    return render_template('profile.html')

@app.route('/events')
def events():
    """Event planning page"""
    return render_template('events.html')

@app.route('/onboarding')
def onboarding():
    """Onboarding flow for new users"""
    return render_template('onboarding.html')

# API Routes
@app.route('/api/user/profile', methods=['GET'])
def get_user_profile():
    """Get user profile data"""
    try:
        user_id = request.args.get('user_id')

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        if DEMO_MODE:
            # Return mock data for demo
            return jsonify({
                'user': {'id': user_id, 'email': '<EMAIL>'},
                'stats': {'total_events': 5, 'alerts_sent': 8, 'events_attended': 3},
                'preferences': {'activities': ['hiking', 'concerts', 'photography']},
                'saved_events': [
                    {
                        'id': '1',
                        'event_title': 'Mountain Hiking Adventure',
                        'event_date': '2025-10-15',
                        'event_location': 'Rocky Mountains',
                        'event_type': 'hiking',
                        'weather_status': 'sunny',
                        'status': 'saved',
                        'created_at': '2025-10-01T10:00:00Z'
                    }
                ],
                'recent_alerts': [
                    {
                        'id': '1',
                        'message': 'Weather alert: Perfect conditions for your hiking event!',
                        'type': 'success',
                        'created_at': '2025-10-03T08:00:00Z',
                        'read': False
                    }
                ]
            })

        # Real Supabase implementation
        user_response = supabase.table('users').select('*').eq('id', user_id).execute()
        stats_response = supabase.table('stats').select('*').eq('user_id', user_id).execute()
        prefs_response = supabase.table('preferences').select('*').eq('user_id', user_id).execute()
        events_response = supabase.table('saved_events').select('*').eq('user_id', user_id).execute()
        alerts_response = supabase.table('alerts').select('*').eq('user_id', user_id).order('created_at', desc=True).limit(5).execute()

        return jsonify({
            'user': user_response.data[0] if user_response.data else None,
            'stats': stats_response.data[0] if stats_response.data else {'total_events': 0, 'alerts_sent': 0, 'events_attended': 0},
            'preferences': prefs_response.data[0] if prefs_response.data else {'activities': []},
            'saved_events': events_response.data,
            'recent_alerts': alerts_response.data
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/user/preferences', methods=['POST'])
def update_preferences():
    """Update user preferences"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        activities = data.get('activities', [])

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        # Check if preferences exist
        existing = supabase.table('preferences').select('*').eq('user_id', user_id).execute()

        preferences_data = {
            'user_id': user_id,
            'activities': activities,
            'updated_at': datetime.now().isoformat()
        }

        if existing.data:
            # Update existing preferences
            response = supabase.table('preferences').update(preferences_data).eq('user_id', user_id).execute()
        else:
            # Create new preferences
            preferences_data['created_at'] = datetime.now().isoformat()
            response = supabase.table('preferences').insert(preferences_data).execute()

        return jsonify({'success': True, 'data': response.data})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/events/save', methods=['POST'])
def save_event():
    """Save an event to user's favorites"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        event_data = data.get('event')

        if not user_id or not event_data:
            return jsonify({'error': 'User ID and event data required'}), 400

        saved_event = {
            'user_id': user_id,
            'event_title': event_data.get('title'),
            'event_date': event_data.get('date'),
            'event_location': event_data.get('location'),
            'event_type': event_data.get('type'),
            'weather_status': event_data.get('weather_status', 'unknown'),
            'status': 'saved',
            'created_at': datetime.now().isoformat()
        }

        response = supabase.table('saved_events').insert(saved_event).execute()

        # Update user stats
        supabase.table('stats').update({
            'total_events': supabase.table('stats').select('total_events').eq('user_id', user_id).execute().data[0]['total_events'] + 1
        }).eq('user_id', user_id).execute()

        return jsonify({'success': True, 'data': response.data})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/alerts/create', methods=['POST'])
def create_alert():
    """Create a new alert for user"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        message = data.get('message')
        alert_type = data.get('type', 'info')

        if not user_id or not message:
            return jsonify({'error': 'User ID and message required'}), 400

        alert_data = {
            'user_id': user_id,
            'message': message,
            'type': alert_type,
            'created_at': datetime.now().isoformat(),
            'read': False
        }

        response = supabase.table('alerts').insert(alert_data).execute()

        # Update alerts count in stats
        current_stats = supabase.table('stats').select('alerts_sent').eq('user_id', user_id).execute()
        if current_stats.data:
            new_count = current_stats.data[0]['alerts_sent'] + 1
            supabase.table('stats').update({'alerts_sent': new_count}).eq('user_id', user_id).execute()

        return jsonify({'success': True, 'data': response.data})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/events/filter', methods=['GET'])
def filter_events():
    """Filter events based on user preferences"""
    try:
        user_id = request.args.get('user_id')
        
        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        # Get user preferences
        prefs_response = supabase.table('preferences').select('*').eq('user_id', user_id).execute()
        
        if not prefs_response.data:
            return jsonify({'events': []})

        user_activities = prefs_response.data[0].get('activities', [])

        # Get all saved events for user
        events_response = supabase.table('saved_events').select('*').eq('user_id', user_id).execute()

        # Filter events based on preferences
        filtered_events = []
        for event in events_response.data:
            event_type = event.get('event_type', '').lower()
            if any(activity.lower() in event_type for activity in user_activities):
                filtered_events.append(event)

        return jsonify({'events': filtered_events})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, port=5000)
