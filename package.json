{"name": "eventplanner-react", "version": "1.0.0", "description": "EventPlanner - User Experience & Personalization with React", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "vite"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "@supabase/supabase-js": "^2.38.0", "axios": "^1.6.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8"}, "keywords": ["react", "event-planning", "weather", "personalization", "hackathon"], "author": "Member 5", "license": "MIT"}