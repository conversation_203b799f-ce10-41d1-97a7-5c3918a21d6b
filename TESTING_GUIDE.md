# 🧪 Complete Testing Guide - EventPlanner

## 🎯 Testing Overview

This guide provides comprehensive testing scenarios to verify all features work correctly for your hackathon demo.

## ⚡ Quick Test (5 minutes)

### Essential Features Test
1. **Start App**: `python app.py` → Visit `http://localhost:5000`
2. **Create Account**: Use `<EMAIL>` / `password123`
3. **Complete Onboarding**: Select 2-3 activities
4. **Plan Event**: Use "Weekend Hike" template
5. **Check Profile**: Verify stats and saved events

## 📋 Detailed Testing Scenarios

### 🔐 Authentication Testing

#### Test 1: User Registration
**Steps:**
1. Go to homepage
2. Fill signup form:
   - Email: `<EMAIL>`
   - Password: `securepass123`
3. Click "Create Account"

**Expected Results:**
- ✅ Success message appears
- ✅ Redirects to onboarding (after 1 second)
- ✅ Navigation shows user is logged in

#### Test 2: User Login
**Steps:**
1. Use existing account or create one first
2. Fill signin form with correct credentials
3. Click "Sign In"

**Expected Results:**
- ✅ Success message appears
- ✅ Redirects to profile (if onboarding complete) or onboarding (if new)
- ✅ Navigation updates to show authenticated state

#### Test 3: Authentication Persistence
**Steps:**
1. Login successfully
2. Refresh the page
3. Navigate between pages

**Expected Results:**
- ✅ User remains logged in
- ✅ Navigation shows correct state
- ✅ Profile data loads correctly

### 🎯 Onboarding Flow Testing

#### Test 4: Complete Onboarding Flow
**Steps:**
1. Create new account (or clear localStorage)
2. Go through each onboarding step:
   - **Step 1**: Welcome screen → Click "Get Started"
   - **Step 2**: Select activities (hiking, concerts, photography) → Click "Continue"
   - **Step 3**: Features overview → Click "Continue"
   - **Step 4**: Review selections → Click "Start Planning Events"

**Expected Results:**
- ✅ Progress indicator updates correctly
- ✅ Selected activities appear in final step
- ✅ Redirects to profile after completion
- ✅ Preferences are saved

#### Test 5: Onboarding Validation
**Steps:**
1. Start onboarding
2. Try to proceed from Step 2 without selecting activities
3. Select activities and proceed

**Expected Results:**
- ✅ Warning message appears when no activities selected
- ✅ Cannot proceed without selection
- ✅ Can proceed after making selections

#### Test 6: Onboarding Navigation
**Steps:**
1. Start onboarding
2. Use "Back" and "Continue" buttons
3. Test keyboard navigation (arrow keys)

**Expected Results:**
- ✅ Back button works correctly
- ✅ Progress indicator updates
- ✅ Keyboard navigation works

### 👤 Profile Page Testing

#### Test 7: Profile Data Display
**Steps:**
1. Complete onboarding
2. View profile page
3. Check all sections

**Expected Results:**
- ✅ User email displays correctly
- ✅ Stats show reasonable numbers
- ✅ Selected preferences are checked
- ✅ Mock events appear in saved events

#### Test 8: Preference Updates
**Steps:**
1. Go to profile page
2. Change activity preferences (check/uncheck boxes)
3. Click "Save Preferences"

**Expected Results:**
- ✅ Success message appears
- ✅ Changes are saved
- ✅ Page refresh maintains changes

#### Test 9: Event Filtering
**Steps:**
1. Ensure you have saved events and preferences
2. Click "Filter by Preferences"
3. Click again to show all events

**Expected Results:**
- ✅ Shows only events matching preferences
- ✅ Button text changes to "Show All Events"
- ✅ Clicking again shows all events

#### Test 10: Alert System
**Steps:**
1. Click "Test Alert System"
2. Click "Check Weather Alerts"
3. Click "Mark All Read"

**Expected Results:**
- ✅ New alerts appear in recent alerts section
- ✅ Alert count in stats increases
- ✅ Alerts marked as read show checkmark

### 📅 Event Planning Testing

#### Test 11: Event Creation
**Steps:**
1. Go to events page
2. Fill out event form:
   - Title: "Test Hiking Trip"
   - Date: Tomorrow's date
   - Location: "Rocky Mountains"
   - Type: "Hiking"
   - Description: "A fun hiking adventure"
3. Click "Save Event"

**Expected Results:**
- ✅ Success modal appears
- ✅ Event appears in recent events
- ✅ Can view event in profile

#### Test 12: Event Templates
**Steps:**
1. Go to events page
2. Click "Weekend Hike" template
3. Verify form is filled
4. Save the event

**Expected Results:**
- ✅ Form fields populate correctly
- ✅ Success message appears
- ✅ Event saves successfully

#### Test 13: Weather Integration
**Steps:**
1. Fill event form with date and location
2. Click "Check Weather"
3. Wait for weather display

**Expected Results:**
- ✅ Loading indicator appears
- ✅ Weather card displays with mock data
- ✅ Weather warnings appear for rainy conditions

#### Test 14: Event Recommendations
**Steps:**
1. Ensure preferences are set
2. View events page recommendations
3. Click "Use This Idea" on a recommendation

**Expected Results:**
- ✅ Recommendations appear
- ✅ Preferred activities show star icon
- ✅ Clicking recommendation fills form

### 🔔 Alert & Notification Testing

#### Test 15: Weather Alerts
**Steps:**
1. Create event with weather alerts enabled
2. Wait for automatic weather check
3. Check profile for new alerts

**Expected Results:**
- ✅ Weather alerts generate for risky conditions
- ✅ Alerts appear in profile
- ✅ Alert count increases in stats

#### Test 16: Alert Management
**Steps:**
1. Generate multiple alerts
2. Mark individual alerts as read
3. Mark all alerts as read

**Expected Results:**
- ✅ Individual alerts can be marked read
- ✅ "Mark All Read" works correctly
- ✅ Read status persists

### 🎨 UI/UX Testing

#### Test 17: Responsive Design
**Steps:**
1. Test on different screen sizes
2. Use browser dev tools to simulate mobile
3. Check all pages

**Expected Results:**
- ✅ Layout adapts to screen size
- ✅ Navigation works on mobile
- ✅ Forms are usable on small screens

#### Test 18: Visual Feedback
**Steps:**
1. Perform various actions
2. Check for loading states
3. Verify success/error messages

**Expected Results:**
- ✅ Loading spinners appear
- ✅ Success messages show
- ✅ Error messages display correctly

## 🐛 Common Issues & Solutions

### Issue: "Supabase not configured"
**Symptoms**: Error messages about Supabase
**Solution**: 
1. Check `.env` file exists
2. Verify Supabase credentials are correct
3. Restart Flask app

### Issue: Onboarding loops infinitely
**Symptoms**: Keeps redirecting to onboarding
**Solution**:
1. Open browser dev tools
2. Go to Application → Local Storage
3. Clear `onboarding_completed` and `user_preferences`

### Issue: Events not saving
**Symptoms**: Success modal doesn't appear
**Solution**:
1. Check browser console for errors
2. Verify form validation passes
3. Check network tab for API calls

### Issue: Preferences not updating
**Symptoms**: Changes don't persist
**Solution**:
1. Check localStorage in dev tools
2. Verify JavaScript console for errors
3. Try refreshing the page

## 📊 Test Results Checklist

### ✅ Authentication
- [ ] User registration works
- [ ] User login works
- [ ] Session persistence works
- [ ] Logout works

### ✅ Onboarding
- [ ] All 4 steps complete
- [ ] Validation works
- [ ] Navigation works
- [ ] Preferences save

### ✅ Profile
- [ ] Data displays correctly
- [ ] Preferences update
- [ ] Event filtering works
- [ ] Alerts generate

### ✅ Events
- [ ] Event creation works
- [ ] Templates work
- [ ] Weather integration works
- [ ] Recommendations appear

### ✅ Alerts
- [ ] Alerts generate
- [ ] Alerts display
- [ ] Mark as read works
- [ ] Stats update

### ✅ UI/UX
- [ ] Responsive design
- [ ] Loading states
- [ ] Success messages
- [ ] Error handling

## 🎬 Demo Preparation

### Before Demo:
1. **Clear Data**: Clear localStorage to start fresh
2. **Test Run**: Complete one full test cycle
3. **Prepare Account**: Have test account ready
4. **Check Performance**: Ensure app loads quickly

### During Demo:
1. **Start Fresh**: Use new account for authentic experience
2. **Highlight Features**: Focus on personalization and weather integration
3. **Show Responsiveness**: Demonstrate mobile-friendly design
4. **Explain Benefits**: Emphasize user experience improvements

### Demo Script (3 minutes):
1. **Registration & Onboarding** (60s): Show new user experience
2. **Event Planning** (60s): Create event with weather check
3. **Personalization** (60s): Show profile, preferences, and filtering

## 🚀 Performance Tips

- **Fast Demo**: Use templates and pre-filled data
- **Smooth Experience**: Test all flows beforehand
- **Backup Plan**: Have screenshots ready if technical issues occur
- **Engagement**: Explain the "why" behind each feature

---

**Ready for your hackathon demo! 🎉**
