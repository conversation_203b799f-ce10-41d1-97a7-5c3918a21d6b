import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import MessageContainer from './MessageContainer'

function Header() {
  const { user, signOut } = useAuth()

  return (
    <>
      <header className="header">
        <nav className="nav">
          <Link to="/" className="logo">🌤️ EventPlanner</Link>
          <ul className="nav-links">
            <li><Link to="/">Home</Link></li>
            {user && (
              <>
                <li><Link to="/profile">Profile</Link></li>
                <li><Link to="/events">Events</Link></li>
                <li>
                  <button 
                    onClick={signOut}
                    className="btn btn-secondary"
                    style={{ padding: '8px 16px', fontSize: '0.9rem' }}
                  >
                    Sign Out
                  </button>
                </li>
              </>
            )}
          </ul>
        </nav>
      </header>
      <MessageContainer />
    </>
  )
}

export default Header
