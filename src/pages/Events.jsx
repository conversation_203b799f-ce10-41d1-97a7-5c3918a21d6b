import React, { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useUser } from '../contexts/UserContext'

function Events() {
  const { showMessage } = useAuth()
  const { preferences, saveEvent, addAlert } = useUser()
  
  const [eventData, setEventData] = useState({
    title: '',
    date: '',
    location: '',
    type: '',
    description: ''
  })
  
  const [weatherForecast, setWeatherForecast] = useState(null)
  const [showSuccessModal, setShowSuccessModal] = useState(false)

  const eventTypes = [
    { id: 'hiking', name: 'Hiking & Nature', emoji: '🥾' },
    { id: 'concerts', name: 'Concerts & Music', emoji: '🎵' },
    { id: 'sports', name: 'Sports & Fitness', emoji: '⚽' },
    { id: 'food', name: 'Food & Dining', emoji: '🍽️' },
    { id: 'art', name: 'Art & Culture', emoji: '🎨' },
    { id: 'photography', name: 'Photography', emoji: '📸' },
    { id: 'fishing', name: 'Fishing', emoji: '🎣' },
    { id: 'festivals', name: 'Festivals', emoji: '🎪' }
  ]

  const eventTemplates = {
    hiking: {
      title: 'Mountain Hiking Adventure',
      location: 'Rocky Mountain Trail',
      type: 'hiking',
      description: 'A scenic hike through mountain trails with beautiful views and fresh air.'
    },
    picnic: {
      title: 'Family Picnic in the Park',
      location: 'Central Park',
      type: 'food',
      description: 'A relaxing outdoor picnic with family and friends, featuring homemade food and games.'
    },
    concert: {
      title: 'Live Music Concert',
      location: 'Downtown Music Hall',
      type: 'concerts',
      description: 'An evening of live music featuring local and touring artists.'
    },
    photography: {
      title: 'Photography Workshop',
      location: 'Art District',
      type: 'photography',
      description: 'Learn photography techniques while exploring scenic locations around the city.'
    }
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setEventData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const applyTemplate = (templateKey) => {
    const template = eventTemplates[templateKey]
    if (template) {
      setEventData(prev => ({
        ...prev,
        ...template,
        date: '' // Keep the date empty for user to fill
      }))
      showMessage(`Applied ${template.title} template!`, 'success')
    }
  }

  const checkWeather = () => {
    if (!eventData.date || !eventData.location) {
      showMessage('Please enter event date and location first', 'warning')
      return
    }

    // Mock weather data
    const weatherConditions = ['sunny', 'cloudy', 'rainy', 'partly-cloudy']
    const randomWeather = weatherConditions[Math.floor(Math.random() * weatherConditions.length)]
    
    const mockForecast = {
      condition: randomWeather,
      temperature: Math.floor(Math.random() * 20) + 60, // 60-80°F
      humidity: Math.floor(Math.random() * 40) + 40, // 40-80%
      windSpeed: Math.floor(Math.random() * 15) + 5, // 5-20 mph
      recommendation: getWeatherRecommendation(randomWeather, eventData.type)
    }

    setWeatherForecast(mockForecast)
    
    // Add weather alert if conditions are concerning
    if (randomWeather === 'rainy' && ['hiking', 'sports', 'photography'].includes(eventData.type)) {
      addAlert(`Weather alert: Rain expected for your ${eventData.type} event on ${eventData.date}`, 'warning')
    }
  }

  const getWeatherRecommendation = (condition, eventType) => {
    if (condition === 'rainy') {
      if (['hiking', 'sports', 'photography'].includes(eventType)) {
        return 'Consider rescheduling or bringing waterproof gear'
      }
      return 'Perfect weather for indoor activities'
    }
    if (condition === 'sunny') {
      return 'Great weather for outdoor activities! Don\'t forget sunscreen'
    }
    return 'Good conditions for your planned activity'
  }

  const getWeatherIcon = (condition) => {
    const icons = {
      sunny: '☀️',
      cloudy: '☁️',
      rainy: '🌧️',
      'partly-cloudy': '⛅'
    }
    return icons[condition] || '🌤️'
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!eventData.title || !eventData.date || !eventData.location || !eventData.type) {
      showMessage('Please fill in all required fields', 'warning')
      return
    }

    const result = await saveEvent(eventData)
    if (result.success) {
      setShowSuccessModal(true)
      
      // Generate personalized recommendation
      if (preferences.includes(eventData.type)) {
        addAlert(`Great choice! We found more ${eventData.type} events you might like`, 'info')
      }
      
      // Reset form
      setEventData({
        title: '',
        date: '',
        location: '',
        type: '',
        description: ''
      })
      setWeatherForecast(null)
    } else {
      showMessage('Error saving event. Please try again.', 'error')
    }
  }

  const getPersonalizedRecommendations = () => {
    if (preferences.length === 0) return []
    
    const recommendations = [
      { type: 'hiking', title: 'Weekend Nature Walk', location: 'Forest Trail' },
      { type: 'concerts', title: 'Jazz Night', location: 'Blue Note Cafe' },
      { type: 'photography', title: 'Sunset Photo Session', location: 'Waterfront Park' },
      { type: 'food', title: 'Food Truck Festival', location: 'Downtown Square' }
    ]
    
    return recommendations.filter(rec => preferences.includes(rec.type)).slice(0, 3)
  }

  return (
    <div>
      {/* Page Header */}
      <div className="card text-center">
        <h1>📅 Event Planning</h1>
        <p>Plan your perfect event with weather integration and personalized recommendations</p>
      </div>

      {/* Event Planning Form */}
      <div className="grid grid-2">
        {/* Left Column: Event Form */}
        <div className="card">
          <h2>🎯 Plan Your Event</h2>

          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="title">Event Title *</label>
              <input
                type="text"
                id="title"
                name="title"
                value={eventData.title}
                onChange={handleInputChange}
                placeholder="Enter event title"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="date">Event Date *</label>
              <input
                type="date"
                id="date"
                name="date"
                value={eventData.date}
                onChange={handleInputChange}
                min={new Date().toISOString().split('T')[0]}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="location">Location *</label>
              <input
                type="text"
                id="location"
                name="location"
                value={eventData.location}
                onChange={handleInputChange}
                placeholder="Enter event location"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="type">Event Type *</label>
              <select
                id="type"
                name="type"
                value={eventData.type}
                onChange={handleInputChange}
                required
              >
                <option value="">Select event type</option>
                {eventTypes.map(type => (
                  <option key={type.id} value={type.id}>
                    {type.emoji} {type.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="description">Description</label>
              <textarea
                id="description"
                name="description"
                value={eventData.description}
                onChange={handleInputChange}
                placeholder="Describe your event (optional)"
                rows="3"
              />
            </div>

            <div className="flex gap-1">
              <button type="submit" className="btn">
                💾 Save Event
              </button>
              <button 
                type="button" 
                onClick={checkWeather}
                className="btn btn-secondary"
              >
                🌤️ Check Weather
              </button>
            </div>
          </form>
        </div>

        {/* Right Column: Weather & Templates */}
        <div>
          {/* Weather Forecast */}
          {weatherForecast && (
            <div className="card">
              <h3>🌤️ Weather Forecast</h3>
              <div className="text-center">
                <div style={{ fontSize: '3rem', margin: '1rem 0' }}>
                  {getWeatherIcon(weatherForecast.condition)}
                </div>
                <h4>{weatherForecast.condition.charAt(0).toUpperCase() + weatherForecast.condition.slice(1)}</h4>
                <p>Temperature: {weatherForecast.temperature}°F</p>
                <p>Humidity: {weatherForecast.humidity}%</p>
                <p>Wind: {weatherForecast.windSpeed} mph</p>
                
                <div className={`alert alert-${weatherForecast.condition === 'rainy' ? 'warning' : 'info'}`}>
                  <strong>Recommendation:</strong><br />
                  {weatherForecast.recommendation}
                </div>
              </div>
            </div>
          )}

          {/* Quick Save Templates */}
          <div className="card">
            <h3>⚡ Quick Templates</h3>
            <p>Click to auto-fill the form with popular event templates:</p>

            <div className="grid">
              <button 
                type="button"
                className="btn btn-secondary template-btn" 
                onClick={() => applyTemplate('hiking')}
              >
                🥾 Weekend Hike
              </button>
              <button 
                type="button"
                className="btn btn-secondary template-btn" 
                onClick={() => applyTemplate('picnic')}
              >
                🧺 Family Picnic
              </button>
              <button 
                type="button"
                className="btn btn-secondary template-btn" 
                onClick={() => applyTemplate('concert')}
              >
                🎵 Live Concert
              </button>
              <button 
                type="button"
                className="btn btn-secondary template-btn" 
                onClick={() => applyTemplate('photography')}
              >
                📸 Photo Walk
              </button>
            </div>
          </div>

          {/* Personalized Recommendations */}
          {preferences.length > 0 && (
            <div className="card">
              <h3>🎯 Recommended for You</h3>
              <p>Based on your interests:</p>
              
              {getPersonalizedRecommendations().map((rec, index) => (
                <div key={index} className="event-card">
                  <div className="flex justify-between align-center">
                    <div>
                      <div className="event-title">{rec.title}</div>
                      <div className="event-date">📍 {rec.location}</div>
                    </div>
                    <div>
                      {eventTypes.find(t => t.id === rec.type)?.emoji}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Success Modal */}
      {showSuccessModal && (
        <div 
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}
          onClick={() => setShowSuccessModal(false)}
        >
          <div className="card text-center" style={{ maxWidth: '400px', margin: '2rem' }}>
            <h3>🎉 Event Saved Successfully!</h3>
            <p>Your event has been saved to your profile.</p>
            
            <div className="mt-2">
              <button 
                onClick={() => {
                  setShowSuccessModal(false)
                  window.location.href = '/profile'
                }}
                className="btn"
              >
                👤 View Profile
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Events
