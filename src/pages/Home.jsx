import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { useUser } from '../contexts/UserContext'

function Home() {
  const { user, signIn, signUp, showMessage } = useAuth()
  const { stats, alerts } = useUser()
  const navigate = useNavigate()
  
  const [signInData, setSignInData] = useState({ email: '', password: '' })
  const [signUpData, setSignUpData] = useState({ email: '', password: '' })

  // Check onboarding status when user signs in
  useEffect(() => {
    if (user) {
      const onboardingCompleted = localStorage.getItem('onboarding_completed')
      if (!onboardingCompleted) {
        navigate('/onboarding')
      }
    }
  }, [user, navigate])

  const handleSignIn = async (e) => {
    e.preventDefault()
    const result = await signIn(signInData.email, signInData.password)
    if (result.success) {
      // Check if onboarding is needed
      const onboardingCompleted = localStorage.getItem('onboarding_completed')
      if (!onboardingCompleted) {
        navigate('/onboarding')
      } else {
        navigate('/profile')
      }
    }
  }

  const handleSignUp = async (e) => {
    e.preventDefault()
    const result = await signUp(signUpData.email, signUpData.password)
    if (result.success) {
      // Clear any existing onboarding data for new users
      localStorage.removeItem('onboarding_completed')
      localStorage.removeItem('user_preferences')
      navigate('/onboarding')
    }
  }

  if (user) {
    return (
      <div>
        {/* Authenticated User Dashboard */}
        <div className="card text-center">
          <h1>Welcome back, {user.email}! 👋</h1>
          <p>Ready to plan your next adventure?</p>
          
          <div className="flex justify-between mt-2">
            <button 
              onClick={() => navigate('/profile')} 
              className="btn"
            >
              View Profile
            </button>
            <button 
              onClick={() => navigate('/events')} 
              className="btn btn-secondary"
            >
              Plan Event
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-number">{stats.total_events}</div>
            <div className="stat-label">Total Events</div>
          </div>
          <div className="stat-card">
            <div className="stat-number">{stats.alerts_sent}</div>
            <div className="stat-label">Alerts Received</div>
          </div>
          <div className="stat-card">
            <div className="stat-number">{stats.events_attended}</div>
            <div className="stat-label">Events Attended</div>
          </div>
        </div>

        {/* Recent Alerts */}
        <div className="card">
          <h3>Recent Alerts</h3>
          {alerts.length > 0 ? (
            alerts.slice(0, 3).map(alert => (
              <div key={alert.id} className={`alert alert-${alert.type}`}>
                <strong>{new Date(alert.created_at).toLocaleDateString()}</strong><br />
                {alert.message}
              </div>
            ))
          ) : (
            <p>No recent alerts</p>
          )}
        </div>
      </div>
    )
  }

  return (
    <div>
      {/* Welcome Section */}
      <div className="card text-center">
        <h1>🌤️ Welcome to EventPlanner</h1>
        <p>Plan your perfect events with real-time weather integration and personalized alerts!</p>
        
        <div className="grid grid-2 mt-2">
          {/* Sign In Form */}
          <div className="card">
            <h2>Sign In</h2>
            <form onSubmit={handleSignIn}>
              <div className="form-group">
                <label htmlFor="signin-email">Email:</label>
                <input
                  type="email"
                  id="signin-email"
                  value={signInData.email}
                  onChange={(e) => setSignInData({...signInData, email: e.target.value})}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="signin-password">Password:</label>
                <input
                  type="password"
                  id="signin-password"
                  value={signInData.password}
                  onChange={(e) => setSignInData({...signInData, password: e.target.value})}
                  required
                />
              </div>
              <button type="submit" className="btn">Sign In</button>
            </form>
          </div>

          {/* Sign Up Form */}
          <div className="card">
            <h2>Create Account</h2>
            <form onSubmit={handleSignUp}>
              <div className="form-group">
                <label htmlFor="signup-email">Email:</label>
                <input
                  type="email"
                  id="signup-email"
                  value={signUpData.email}
                  onChange={(e) => setSignUpData({...signUpData, email: e.target.value})}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="signup-password">Password:</label>
                <input
                  type="password"
                  id="signup-password"
                  value={signUpData.password}
                  onChange={(e) => setSignUpData({...signUpData, password: e.target.value})}
                  minLength="6"
                  required
                />
              </div>
              <button type="submit" className="btn">Create Account</button>
            </form>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="card">
        <h2>Features</h2>
        <div className="grid grid-3">
          <div className="text-center">
            <h3>🎯 Personalized Events</h3>
            <p>Get event recommendations based on your interests and preferences</p>
          </div>
          <div className="text-center">
            <h3>🌦️ Weather Integration</h3>
            <p>Real-time weather alerts for your planned outdoor activities</p>
          </div>
          <div className="text-center">
            <h3>📊 Activity Tracking</h3>
            <p>Track your event history and get insights on your activities</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Home
