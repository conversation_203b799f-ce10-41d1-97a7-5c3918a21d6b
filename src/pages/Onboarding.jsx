import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { useUser } from '../contexts/UserContext'

function Onboarding() {
  const { user, showMessage } = useAuth()
  const { updatePreferences } = useUser()
  const navigate = useNavigate()
  
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedActivities, setSelectedActivities] = useState([])

  const activities = [
    { id: 'hiking', name: 'Hiking & Nature', emoji: '🥾', description: 'Outdoor adventures and nature walks' },
    { id: 'concerts', name: 'Concerts & Music', emoji: '🎵', description: 'Live music and entertainment events' },
    { id: 'sports', name: 'Sports & Fitness', emoji: '⚽', description: 'Athletic events and fitness activities' },
    { id: 'food', name: 'Food & Dining', emoji: '🍽️', description: 'Culinary experiences and food festivals' },
    { id: 'art', name: 'Art & Culture', emoji: '🎨', description: 'Museums, galleries, and cultural events' },
    { id: 'photography', name: 'Photography', emoji: '📸', description: 'Photo walks and photography workshops' },
    { id: 'fishing', name: 'Fishing', emoji: '🎣', description: 'Fishing trips and water activities' },
    { id: 'festivals', name: 'Festivals', emoji: '🎪', description: 'Community festivals and celebrations' }
  ]

  const toggleActivity = (activityId) => {
    setSelectedActivities(prev => 
      prev.includes(activityId)
        ? prev.filter(id => id !== activityId)
        : [...prev, activityId]
    )
  }

  const nextStep = () => {
    if (currentStep === 2 && selectedActivities.length === 0) {
      showMessage('Please select at least one activity to continue', 'warning')
      return
    }
    setCurrentStep(prev => prev + 1)
  }

  const prevStep = () => {
    setCurrentStep(prev => prev - 1)
  }

  const completeOnboarding = async () => {
    const result = await updatePreferences(selectedActivities)
    if (result.success) {
      localStorage.setItem('onboarding_completed', 'true')
      showMessage('Welcome to EventPlanner! Your preferences have been saved.', 'success')
      navigate('/profile')
    } else {
      showMessage('Error saving preferences. Please try again.', 'error')
    }
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="text-center">
            <h2>🎉 Welcome to EventPlanner!</h2>
            <p>Let's set up your personalized experience in just a few steps.</p>
            <div className="mt-2">
              <p>We'll help you:</p>
              <ul style={{ textAlign: 'left', maxWidth: '400px', margin: '1rem auto' }}>
                <li>✨ Choose your favorite activities</li>
                <li>🎯 Get personalized event recommendations</li>
                <li>🌦️ Receive weather alerts for your events</li>
                <li>📊 Track your event history</li>
              </ul>
            </div>
          </div>
        )

      case 2:
        return (
          <div>
            <h2>🎯 Choose Your Interests</h2>
            <p>Select the activities you enjoy most. This helps us recommend events you'll love!</p>
            
            <div className="grid grid-2 mt-2">
              {activities.map(activity => (
                <div
                  key={activity.id}
                  className={`card ${selectedActivities.includes(activity.id) ? 'selected' : ''}`}
                  onClick={() => toggleActivity(activity.id)}
                  style={{
                    cursor: 'pointer',
                    border: selectedActivities.includes(activity.id) 
                      ? '3px solid #667eea' 
                      : '1px solid rgba(255, 255, 255, 0.18)',
                    transform: selectedActivities.includes(activity.id) ? 'scale(1.02)' : 'scale(1)',
                    transition: 'all 0.2s ease'
                  }}
                >
                  <div className="text-center">
                    <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
                      {activity.emoji}
                    </div>
                    <h3>{activity.name}</h3>
                    <p style={{ fontSize: '0.9rem', color: '#666' }}>
                      {activity.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="text-center mt-2">
              <p>Selected: {selectedActivities.length} activities</p>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="text-center">
            <h2>🌟 Great Choices!</h2>
            <p>You've selected {selectedActivities.length} activities:</p>
            
            <div className="grid grid-3 mt-2">
              {selectedActivities.map(activityId => {
                const activity = activities.find(a => a.id === activityId)
                return (
                  <div key={activityId} className="card text-center">
                    <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
                      {activity.emoji}
                    </div>
                    <h4>{activity.name}</h4>
                  </div>
                )
              })}
            </div>
            
            <div className="mt-2">
              <h3>🎯 What's Next?</h3>
              <ul style={{ textAlign: 'left', maxWidth: '500px', margin: '1rem auto' }}>
                <li>📅 Plan events using our smart templates</li>
                <li>🌦️ Get weather alerts for outdoor activities</li>
                <li>🔔 Receive notifications about events you might like</li>
                <li>📊 Track your event history and preferences</li>
              </ul>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="text-center">
            <h2>🎉 You're All Set!</h2>
            <p>Welcome to your personalized EventPlanner experience!</p>
            
            <div className="grid grid-2 mt-2">
              <div className="card">
                <h3>🎯 Smart Recommendations</h3>
                <p>We'll suggest events based on your selected interests and past activities.</p>
              </div>
              <div className="card">
                <h3>🌦️ Weather Alerts</h3>
                <p>Get notified about weather conditions that might affect your outdoor events.</p>
              </div>
            </div>
            
            <div className="mt-2">
              <p>Ready to start planning amazing events?</p>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="card">
      {/* Progress Bar */}
      <div style={{ marginBottom: '2rem' }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '1rem'
        }}>
          <span>Step {currentStep} of 4</span>
          <span>{Math.round((currentStep / 4) * 100)}% Complete</span>
        </div>
        <div style={{
          width: '100%',
          height: '8px',
          backgroundColor: '#e0e0e0',
          borderRadius: '4px',
          overflow: 'hidden'
        }}>
          <div style={{
            width: `${(currentStep / 4) * 100}%`,
            height: '100%',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            transition: 'width 0.3s ease'
          }} />
        </div>
      </div>

      {/* Step Content */}
      {renderStep()}

      {/* Navigation Buttons */}
      <div className="flex justify-between mt-2">
        <button
          onClick={prevStep}
          disabled={currentStep === 1}
          className="btn btn-secondary"
          style={{ 
            opacity: currentStep === 1 ? 0.5 : 1,
            cursor: currentStep === 1 ? 'not-allowed' : 'pointer'
          }}
        >
          Previous
        </button>
        
        {currentStep < 4 ? (
          <button onClick={nextStep} className="btn">
            Next
          </button>
        ) : (
          <button onClick={completeOnboarding} className="btn">
            Complete Setup
          </button>
        )}
      </div>
    </div>
  )
}

export default Onboarding
