import React, { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useUser } from '../contexts/UserContext'

function Profile() {
  const { user, showMessage } = useAuth()
  const { 
    profile, 
    preferences, 
    savedEvents, 
    alerts, 
    stats, 
    loading,
    updatePreferences,
    markAlertAsRead,
    markAllAlertsAsRead
  } = useUser()

  const [editingPreferences, setEditingPreferences] = useState(false)
  const [tempPreferences, setTempPreferences] = useState([])
  const [eventFilter, setEventFilter] = useState('all')

  const activities = [
    { id: 'hiking', name: 'Hiking & Nature', emoji: '🥾' },
    { id: 'concerts', name: 'Concerts & Music', emoji: '🎵' },
    { id: 'sports', name: 'Sports & Fitness', emoji: '⚽' },
    { id: 'food', name: 'Food & Dining', emoji: '🍽️' },
    { id: 'art', name: 'Art & Culture', emoji: '🎨' },
    { id: 'photography', name: 'Photography', emoji: '📸' },
    { id: 'fishing', name: 'Fishing', emoji: '🎣' },
    { id: 'festivals', name: 'Festivals', emoji: '🎪' }
  ]

  const startEditingPreferences = () => {
    setTempPreferences([...preferences])
    setEditingPreferences(true)
  }

  const cancelEditingPreferences = () => {
    setTempPreferences([])
    setEditingPreferences(false)
  }

  const savePreferences = async () => {
    const result = await updatePreferences(tempPreferences)
    if (result.success) {
      showMessage('Preferences updated successfully!', 'success')
      setEditingPreferences(false)
    } else {
      showMessage('Error updating preferences. Please try again.', 'error')
    }
  }

  const togglePreference = (activityId) => {
    setTempPreferences(prev => 
      prev.includes(activityId)
        ? prev.filter(id => id !== activityId)
        : [...prev, activityId]
    )
  }

  const getFilteredEvents = () => {
    if (eventFilter === 'all') return savedEvents
    if (eventFilter === 'preferences') {
      return savedEvents.filter(event => preferences.includes(event.event_type))
    }
    return savedEvents.filter(event => event.status === eventFilter)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        Loading your profile...
      </div>
    )
  }

  return (
    <div>
      {/* Profile Header */}
      <div className="card">
        <div className="flex justify-between align-center">
          <div>
            <h1>👤 My Profile</h1>
            <p>Welcome back, {user?.email}</p>
          </div>
          <div>
            <p>Member since: {formatDate(user?.created_at || new Date())}</p>
          </div>
        </div>
      </div>

      {/* User Stats Dashboard */}
      <div className="card">
        <h2>📊 Your Activity Stats</h2>
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-number">{stats.total_events}</div>
            <div className="stat-label">Total Events Saved</div>
          </div>
          <div className="stat-card">
            <div className="stat-number">{stats.alerts_sent}</div>
            <div className="stat-label">Alerts Received</div>
          </div>
          <div className="stat-card">
            <div className="stat-number">{stats.events_attended}</div>
            <div className="stat-label">Events Attended</div>
          </div>
          <div className="stat-card">
            <div className="stat-number">{preferences.length}</div>
            <div className="stat-label">Activity Interests</div>
          </div>
        </div>
      </div>

      {/* Two Column Layout */}
      <div className="grid grid-2">
        {/* Left Column: Preferences */}
        <div className="card">
          <div className="flex justify-between align-center mb-2">
            <h2>🎯 Activity Preferences</h2>
            {!editingPreferences ? (
              <button onClick={startEditingPreferences} className="btn btn-secondary">
                Edit
              </button>
            ) : (
              <div className="flex gap-1">
                <button onClick={savePreferences} className="btn">
                  Save
                </button>
                <button onClick={cancelEditingPreferences} className="btn btn-secondary">
                  Cancel
                </button>
              </div>
            )}
          </div>

          {editingPreferences ? (
            <div className="grid">
              {activities.map(activity => (
                <div
                  key={activity.id}
                  onClick={() => togglePreference(activity.id)}
                  style={{
                    padding: '1rem',
                    border: tempPreferences.includes(activity.id) 
                      ? '2px solid #667eea' 
                      : '2px solid #e1e5e9',
                    borderRadius: '10px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    backgroundColor: tempPreferences.includes(activity.id) 
                      ? 'rgba(102, 126, 234, 0.1)' 
                      : 'white'
                  }}
                >
                  <span style={{ fontSize: '1.2rem', marginRight: '0.5rem' }}>
                    {activity.emoji}
                  </span>
                  {activity.name}
                </div>
              ))}
            </div>
          ) : (
            <div className="grid">
              {preferences.length > 0 ? (
                preferences.map(prefId => {
                  const activity = activities.find(a => a.id === prefId)
                  return activity ? (
                    <div key={prefId} className="flex align-center" style={{ padding: '0.5rem 0' }}>
                      <span style={{ fontSize: '1.2rem', marginRight: '0.5rem' }}>
                        {activity.emoji}
                      </span>
                      {activity.name}
                    </div>
                  ) : null
                })
              ) : (
                <p>No preferences set. Click "Edit" to add your interests!</p>
              )}
            </div>
          )}
        </div>

        {/* Right Column: Recent Alerts */}
        <div className="card">
          <div className="flex justify-between align-center mb-2">
            <h2>🔔 Recent Alerts</h2>
            {alerts.some(alert => !alert.read) && (
              <button 
                onClick={markAllAlertsAsRead}
                className="btn btn-secondary"
                style={{ fontSize: '0.8rem', padding: '6px 12px' }}
              >
                Mark All Read
              </button>
            )}
          </div>

          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            {alerts.length > 0 ? (
              alerts.map(alert => (
                <div
                  key={alert.id}
                  className={`alert alert-${alert.type}`}
                  style={{
                    opacity: alert.read ? 0.7 : 1,
                    cursor: alert.read ? 'default' : 'pointer'
                  }}
                  onClick={() => !alert.read && markAlertAsRead(alert.id)}
                >
                  <div className="flex justify-between align-center">
                    <div>
                      <strong>{formatDate(alert.created_at)}</strong>
                      <p style={{ margin: '0.5rem 0 0 0' }}>{alert.message}</p>
                    </div>
                    {!alert.read && (
                      <div style={{
                        width: '8px',
                        height: '8px',
                        backgroundColor: '#667eea',
                        borderRadius: '50%',
                        flexShrink: 0
                      }} />
                    )}
                  </div>
                </div>
              ))
            ) : (
              <p>No alerts yet. We'll notify you about weather updates and event recommendations!</p>
            )}
          </div>
        </div>
      </div>

      {/* Saved Events */}
      <div className="card">
        <div className="flex justify-between align-center mb-2">
          <h2>📅 Saved Events</h2>
          <select 
            value={eventFilter}
            onChange={(e) => setEventFilter(e.target.value)}
            style={{
              padding: '8px 12px',
              borderRadius: '8px',
              border: '2px solid #e1e5e9',
              backgroundColor: 'white'
            }}
          >
            <option value="all">All Events</option>
            <option value="saved">Upcoming</option>
            <option value="past">Past Events</option>
            <option value="preferences">Matching Preferences</option>
          </select>
        </div>

        <div className="grid">
          {getFilteredEvents().length > 0 ? (
            getFilteredEvents().map(event => (
              <div key={event.id} className="event-card">
                <div className="flex justify-between align-center">
                  <div>
                    <div className="event-title">{event.event_title}</div>
                    <div className="event-date">
                      📅 {formatDate(event.event_date)} • 📍 {event.event_location}
                    </div>
                    <div style={{ marginTop: '0.5rem' }}>
                      <span className={`event-status status-${event.status}`}>
                        {event.status === 'saved' ? 'Upcoming' : 'Past Event'}
                      </span>
                      {event.weather_status && event.weather_status !== 'unknown' && (
                        <span style={{ marginLeft: '0.5rem', fontSize: '0.9rem' }}>
                          🌤️ {event.weather_status}
                        </span>
                      )}
                    </div>
                  </div>
                  <div>
                    {activities.find(a => a.id === event.event_type)?.emoji || '📅'}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <p>No events found. <a href="/events" style={{ color: '#667eea' }}>Plan your first event!</a></p>
          )}
        </div>
      </div>
    </div>
  )
}

export default Profile
