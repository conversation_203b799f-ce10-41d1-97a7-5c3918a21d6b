import React, { createContext, useContext, useState, useEffect } from 'react'
import { createClient } from '@supabase/supabase-js'

const AuthContext = createContext()

// Supabase configuration
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || 'https://ltitsevhrmbrpnzaarmg.supabase.co'
const SUPABASE_KEY = import.meta.env.VITE_SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Vhk4P9QodhDVfXo5mUapvJBJF77tAVRUmzsx1x9UJdo'

// Check if we're in demo mode
const DEMO_MODE = !SUPABASE_URL || SUPABASE_URL.includes('demo') || !SUPABASE_KEY || SUPABASE_KEY === 'demo_key_for_development'

let supabase = null
if (!DEMO_MODE) {
  supabase = createClient(SUPABASE_URL, SUPABASE_KEY)
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [message, setMessage] = useState(null)

  useEffect(() => {
    if (DEMO_MODE) {
      // In demo mode, check localStorage for mock user
      const mockUser = localStorage.getItem('demo_user')
      if (mockUser) {
        setUser(JSON.parse(mockUser))
      }
      setLoading(false)
    } else {
      // Real Supabase auth
      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        async (event, session) => {
          if (session?.user) {
            setUser(session.user)
          } else {
            setUser(null)
          }
          setLoading(false)
        }
      )

      return () => subscription.unsubscribe()
    }
  }, [])

  const signUp = async (email, password) => {
    try {
      if (DEMO_MODE) {
        // Mock signup
        const mockUser = {
          id: Date.now().toString(),
          email: email,
          created_at: new Date().toISOString()
        }
        localStorage.setItem('demo_user', JSON.stringify(mockUser))
        setUser(mockUser)
        showMessage('Account created successfully! Welcome to EventPlanner!', 'success')
        return { success: true }
      } else {
        const { data, error } = await supabase.auth.signUp({
          email: email,
          password: password
        })

        if (error) throw error

        if (data.user) {
          showMessage('Account created successfully! Please check your email to verify.', 'success')
          return { success: true }
        }
      }
    } catch (error) {
      showMessage(error.message, 'error')
      return { success: false, error: error.message }
    }
  }

  const signIn = async (email, password) => {
    try {
      if (DEMO_MODE) {
        // Mock signin
        const mockUser = {
          id: Date.now().toString(),
          email: email,
          created_at: new Date().toISOString()
        }
        localStorage.setItem('demo_user', JSON.stringify(mockUser))
        setUser(mockUser)
        showMessage('Signed in successfully!', 'success')
        return { success: true }
      } else {
        const { data, error } = await supabase.auth.signInWithPassword({
          email: email,
          password: password
        })

        if (error) throw error

        showMessage('Signed in successfully!', 'success')
        return { success: true }
      }
    } catch (error) {
      showMessage(error.message, 'error')
      return { success: false, error: error.message }
    }
  }

  const signOut = async () => {
    try {
      if (DEMO_MODE) {
        localStorage.removeItem('demo_user')
        localStorage.removeItem('user_preferences')
        localStorage.removeItem('onboarding_completed')
        setUser(null)
      } else {
        const { error } = await supabase.auth.signOut()
        if (error) throw error
      }
      showMessage('Signed out successfully!', 'success')
    } catch (error) {
      showMessage(error.message, 'error')
    }
  }

  const showMessage = (text, type = 'info') => {
    setMessage({ text, type })
    setTimeout(() => setMessage(null), 5000)
  }

  const value = {
    user,
    loading,
    message,
    signUp,
    signIn,
    signOut,
    showMessage,
    isDemo: DEMO_MODE
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
