import React, { createContext, useContext, useState, useEffect } from 'react'
import { useAuth } from './AuthContext'

const UserContext = createContext()

export function useUser() {
  const context = useContext(UserContext)
  if (!context) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}

export function UserProvider({ children }) {
  const { user, isDemo } = useAuth()
  const [profile, setProfile] = useState(null)
  const [preferences, setPreferences] = useState([])
  const [savedEvents, setSavedEvents] = useState([])
  const [alerts, setAlerts] = useState([])
  const [stats, setStats] = useState({
    total_events: 0,
    events_attended: 0,
    alerts_sent: 0
  })
  const [loading, setLoading] = useState(false)

  // Mock data for demo mode
  const getMockData = () => ({
    profile: {
      id: user?.id,
      email: user?.email,
      created_at: user?.created_at
    },
    preferences: JSON.parse(localStorage.getItem('user_preferences') || '[]'),
    stats: {
      total_events: 5,
      events_attended: 3,
      alerts_sent: 8
    },
    savedEvents: [
      {
        id: '1',
        event_title: 'Mountain Hiking Adventure',
        event_date: '2025-10-15',
        event_location: 'Rocky Mountains',
        event_type: 'hiking',
        weather_status: 'sunny',
        status: 'saved',
        created_at: '2025-10-01T10:00:00Z'
      },
      {
        id: '2',
        event_title: 'Jazz Festival Downtown',
        event_date: '2025-09-20',
        event_location: 'City Center',
        event_type: 'concerts',
        weather_status: 'cloudy',
        status: 'past',
        created_at: '2025-09-15T14:30:00Z'
      },
      {
        id: '3',
        event_title: 'Photography Workshop',
        event_date: '2025-10-25',
        event_location: 'Art District',
        event_type: 'photography',
        weather_status: 'unknown',
        status: 'saved',
        created_at: '2025-10-02T09:15:00Z'
      }
    ],
    alerts: [
      {
        id: '1',
        message: 'Weather alert: Rain expected for your hiking event on Oct 15th',
        type: 'warning',
        created_at: '2025-10-03T08:00:00Z',
        read: false
      },
      {
        id: '2',
        message: 'New concert event matches your preferences!',
        type: 'info',
        created_at: '2025-10-02T16:30:00Z',
        read: false
      },
      {
        id: '3',
        message: 'Your photography workshop is coming up in 3 days',
        type: 'success',
        created_at: '2025-10-01T12:00:00Z',
        read: true
      }
    ]
  })

  // Load user data when user changes
  useEffect(() => {
    if (user) {
      loadUserData()
    } else {
      // Clear data when user logs out
      setProfile(null)
      setPreferences([])
      setSavedEvents([])
      setAlerts([])
      setStats({ total_events: 0, events_attended: 0, alerts_sent: 0 })
    }
  }, [user])

  const loadUserData = async () => {
    if (!user) return

    setLoading(true)
    try {
      if (isDemo) {
        // Load mock data
        const mockData = getMockData()
        setProfile(mockData.profile)
        setPreferences(mockData.preferences)
        setSavedEvents(mockData.savedEvents)
        setAlerts(mockData.alerts)
        setStats(mockData.stats)
      } else {
        // Load real data from API
        const response = await fetch(`/api/user/profile?user_id=${user.id}`)
        const data = await response.json()
        
        if (data.error) {
          console.error('Error loading user data:', data.error)
          return
        }

        setProfile(data.user)
        setPreferences(data.preferences?.activities || [])
        setSavedEvents(data.saved_events || [])
        setAlerts(data.recent_alerts || [])
        setStats(data.stats || { total_events: 0, events_attended: 0, alerts_sent: 0 })
      }
    } catch (error) {
      console.error('Error loading user data:', error)
    } finally {
      setLoading(false)
    }
  }

  const updatePreferences = async (newPreferences) => {
    try {
      if (isDemo) {
        // Save to localStorage in demo mode
        localStorage.setItem('user_preferences', JSON.stringify(newPreferences))
        setPreferences(newPreferences)
        return { success: true }
      } else {
        // Save to real database
        const response = await fetch('/api/user/preferences', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_id: user.id,
            activities: newPreferences
          })
        })

        const data = await response.json()
        if (data.success) {
          setPreferences(newPreferences)
          return { success: true }
        } else {
          throw new Error(data.error)
        }
      }
    } catch (error) {
      console.error('Error updating preferences:', error)
      return { success: false, error: error.message }
    }
  }

  const saveEvent = async (eventData) => {
    try {
      const newEvent = {
        id: Date.now().toString(),
        user_id: user.id,
        event_title: eventData.title,
        event_date: eventData.date,
        event_location: eventData.location,
        event_type: eventData.type,
        weather_status: 'unknown',
        status: 'saved',
        created_at: new Date().toISOString()
      }

      if (isDemo) {
        // Add to local state in demo mode
        setSavedEvents(prev => [newEvent, ...prev])
        setStats(prev => ({ ...prev, total_events: prev.total_events + 1 }))
        return { success: true }
      } else {
        // Save to real database
        const response = await fetch('/api/events/save', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_id: user.id,
            event: eventData
          })
        })

        const data = await response.json()
        if (data.success) {
          setSavedEvents(prev => [newEvent, ...prev])
          setStats(prev => ({ ...prev, total_events: prev.total_events + 1 }))
          return { success: true }
        } else {
          throw new Error(data.error)
        }
      }
    } catch (error) {
      console.error('Error saving event:', error)
      return { success: false, error: error.message }
    }
  }

  const addAlert = (message, type = 'info') => {
    const newAlert = {
      id: Date.now().toString(),
      message,
      type,
      created_at: new Date().toISOString(),
      read: false
    }

    setAlerts(prev => [newAlert, ...prev])
    setStats(prev => ({ ...prev, alerts_sent: prev.alerts_sent + 1 }))
  }

  const markAlertAsRead = (alertId) => {
    setAlerts(prev => 
      prev.map(alert => 
        alert.id === alertId ? { ...alert, read: true } : alert
      )
    )
  }

  const markAllAlertsAsRead = () => {
    setAlerts(prev => 
      prev.map(alert => ({ ...alert, read: true }))
    )
  }

  const value = {
    profile,
    preferences,
    savedEvents,
    alerts,
    stats,
    loading,
    loadUserData,
    updatePreferences,
    saveEvent,
    addAlert,
    markAlertAsRead,
    markAllAlertsAsRead
  }

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  )
}
